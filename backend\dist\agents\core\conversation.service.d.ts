import { Repository, DataSource } from 'typeorm';
import { AgentRuntimeCallbacks, GenerateMessageInput, ProcessUserMessageInput } from './conversation-agent';
import { Conversation } from '../../conversations/conversation.entity';
import { Store } from '../../stores/store.entity';
import { Message } from '../../conversations/message.entity';
import { Product } from '../../products/product.entity';
import { ToolCall } from '../../conversations/tool-call.entity';
import { DatabaseService } from '../../shared/database.service';
export declare class ConversationService {
    private readonly conversationRepository;
    private readonly storeRepository;
    private readonly messageRepository;
    private readonly productRepository;
    private readonly toolCallRepository;
    private readonly databaseService;
    constructor(conversationRepository: Repository<Conversation>, storeRepository: Repository<Store>, messageRepository: Repository<Message>, productRepository: Repository<Product>, toolCallRepository: Repository<ToolCall>, databaseService: DatabaseService);
    private get dataSource();
    generateAgentMessage(params: ProcessUserMessageInput | GenerateMessageInput, callbacks?: AgentRuntimeCallbacks): Promise<string>;
    generateMessage(input: GenerateMessageInput, callbacks?: AgentRuntimeCallbacks): Promise<string>;
    processUserMessage(params: ProcessUserMessageInput, callbacks?: AgentRuntimeCallbacks): Promise<string>;
    private getConversationByUuid;
    private getStoreCurrency;
    private getStorePreferredLanguage;
    private extractMetricsFromResponse;
    private updateConversationTotals;
    private persistAgentResponse;
    private updateNotificationSafely;
    private getConversationAndStoreInfo;
    private getConversationHistory;
    private getStoreProducts;
}
export declare function generateAgentMessage(db: DataSource, params: ProcessUserMessageInput | GenerateMessageInput, callbacks?: AgentRuntimeCallbacks): Promise<string>;
export declare function generateMessage(db: DataSource, input: GenerateMessageInput, callbacks?: AgentRuntimeCallbacks): Promise<string>;
export declare function processUserMessage(db: DataSource, params: ProcessUserMessageInput, callbacks?: AgentRuntimeCallbacks): Promise<string>;
