"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ConversationService = void 0;
exports.generateAgentMessage = generateAgentMessage;
exports.generateMessage = generateMessage;
exports.processUserMessage = processUserMessage;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const conversation_agent_1 = require("./conversation-agent");
const dist_1 = require("../../../../llm-api/dist");
const conversation_entity_1 = require("../../conversations/conversation.entity");
const store_entity_1 = require("../../stores/store.entity");
const message_entity_1 = require("../../conversations/message.entity");
const product_entity_1 = require("../../products/product.entity");
const tool_call_entity_1 = require("../../conversations/tool-call.entity");
const database_service_1 = require("../../shared/database.service");
let ConversationService = class ConversationService {
    constructor(conversationRepository, storeRepository, messageRepository, productRepository, toolCallRepository, databaseService) {
        this.conversationRepository = conversationRepository;
        this.storeRepository = storeRepository;
        this.messageRepository = messageRepository;
        this.productRepository = productRepository;
        this.toolCallRepository = toolCallRepository;
        this.databaseService = databaseService;
    }
    get dataSource() {
        return this.databaseService.getDataSource();
    }
    async generateAgentMessage(params, callbacks) {
        console.log(`🔧 generateAgentMessage called with:`, {
            dbType: typeof this.dataSource,
            dbExists: !!this.dataSource,
            dbInitialized: this.dataSource?.isInitialized,
            params,
            timestamp: new Date().toISOString()
        });
        await (0, conversation_agent_1.ensureLLMProviderConfigured)();
        const { conversationUuid, userId, customerId, guestUuid, agentId } = params;
        const userMessage = 'userMessage' in params ? params.userMessage : undefined;
        const startTime = Date.now();
        try {
            if (!this.dataSource || !this.dataSource.isInitialized) {
                console.error(`❌ Database connection validation failed in generateAgentMessage:`, {
                    dbExists: !!this.dataSource,
                    dbInitialized: this.dataSource?.isInitialized,
                    dbType: typeof this.dataSource
                });
                throw new Error('Database connection is not properly initialized in generateAgentMessage');
            }
            console.log(`✅ Database connection validated in generateAgentMessage`);
            await this.updateNotificationSafely(conversationUuid, conversation_agent_1.ConversationNotification.AgentIsThinking, callbacks);
            const { conversation, storeCurrency, storePreferredLanguage } = await this.getConversationAndStoreInfo(conversationUuid);
            const products = await this.getStoreProducts(BigInt(conversation.storeId));
            const customer = null;
            console.log(`🔧 Building conversation agent with database connection:`, {
                dbType: typeof this.dataSource,
                dbInitialized: this.dataSource?.isInitialized,
                conversationUuid,
                storeCurrency,
                storePreferredLanguage
            });
            const conversationAgent = (0, conversation_agent_1.buildConversationAgent)(this.dataSource, conversationUuid, storeCurrency, storePreferredLanguage);
            const history = await this.getConversationHistory(BigInt(conversation.id));
            const productContextMessage = (0, conversation_agent_1.buildProductContextMessage)(products, storeCurrency);
            const customerContextMessage = (0, conversation_agent_1.buildCustomerContextMessage)(customer);
            const allMessages = [];
            if (productContextMessage)
                allMessages.push(productContextMessage);
            if (customerContextMessage)
                allMessages.push(customerContextMessage);
            allMessages.push(...history);
            if (userMessage) {
                allMessages.push({ role: 'user', content: userMessage });
            }
            else {
                allMessages.push({ role: 'user', content: 'Continue the conversation.' });
            }
            await this.updateNotificationSafely(conversationUuid, conversation_agent_1.ConversationNotification.AgentIsGeneratingResponse, callbacks);
            const response = await dist_1.LlmApi.generateLlmResponse(conversationAgent, allMessages);
            const sanitizedResponse = response.content;
            const executionTime = Date.now() - startTime;
            const metrics = this.extractMetricsFromResponse(response);
            const created = await this.persistAgentResponse({
                agentId,
                userId,
                customerId,
                guestUuid,
                conversationId: BigInt(conversation.id),
                response: sanitizedResponse,
                toolCalls: response.calls,
                toolOutputs: response.outputs,
                executionDetails: response.executionDetails,
                llmCosts: {
                    ...metrics,
                    totalExecutionTime: executionTime,
                },
            });
            await this.updateNotificationSafely(conversationUuid, conversation_agent_1.ConversationNotification.None, callbacks);
            return created.content;
        }
        catch (error) {
            await this.updateNotificationSafely(conversationUuid, conversation_agent_1.ConversationNotification.None, callbacks);
            throw error;
        }
    }
    async generateMessage(input, callbacks) {
        return this.generateAgentMessage(input, callbacks);
    }
    async processUserMessage(params, callbacks) {
        return this.generateAgentMessage(params, callbacks);
    }
    async getConversationByUuid(conversationUuid) {
        return this.conversationRepository.findOne({
            where: { uuid: conversationUuid, isDeleted: false },
            select: ['id', 'storeId'],
        });
    }
    async getStoreCurrency(storeId) {
        const store = await this.storeRepository.findOne({
            where: { id: storeId.toString(), isDeleted: false },
            select: ['currency'],
        });
        return store?.currency || 'USD';
    }
    async getStorePreferredLanguage(storeId) {
        const store = await this.storeRepository.findOne({
            where: { id: storeId.toString(), isDeleted: false },
            select: ['preferredLanguage'],
        });
        return store?.preferredLanguage || 'en';
    }
    extractMetricsFromResponse(response) {
        return {
            totalCost: response.totalCost || 0,
            totalInputTokens: response.totalInputTokens || 0,
            totalOutputTokens: response.totalOutputTokens || 0,
            totalExecutionTime: response.totalExecutionTime || 0,
        };
    }
    async updateConversationTotals(conversationId) {
        try {
            await this.conversationRepository.update({ id: conversationId.toString() }, {
                updatedAt: new Date(),
            });
        }
        catch (error) {
            console.warn('Failed to update conversation totals:', error);
        }
    }
    async persistAgentResponse(params) {
        const { userId, customerId, guestUuid, conversationId, response, toolCalls, toolOutputs, executionDetails, llmCosts } = params;
        const { ownerId } = (0, conversation_agent_1.getOwnerInfo)({ userId, customerId, guestUuid });
        const nextSequence = Date.now();
        if (toolCalls && toolCalls.length > 0) {
            for (let i = 0; i < toolCalls.length; i++) {
                const toolCall = toolCalls[i];
                const toolOutput = toolOutputs?.find(to => to.tool_call_id === toolCall.id);
                const executionDetail = executionDetails?.find(ed => ed.toolCallId === toolCall.id);
                let toolInput = {};
                try {
                    toolInput = JSON.parse(toolCall.function.arguments);
                }
                catch (e) {
                    toolInput = { raw: toolCall.function.arguments };
                }
                let toolOutputData = undefined;
                if (toolOutput?.content) {
                    try {
                        toolOutputData = JSON.parse(toolOutput.content);
                    }
                    catch (e) {
                        toolOutputData = { rawContent: toolOutput.content };
                    }
                }
                try {
                    await this.toolCallRepository.save({
                        toolName: toolCall.function.name,
                        parameters: toolInput || {},
                        conversationId: conversationId.toString(),
                        createdBy: ownerId,
                        result: toolOutputData,
                        duration: executionDetail?.executionTime || 0,
                        status: executionDetail?.success ?? true ? 'completed' : 'failed',
                        error: executionDetail?.errorMessage,
                        userId: userId ? (0, conversation_agent_1.toBigInt)(userId).toString() : null,
                        customerId: customerId ? (0, conversation_agent_1.toBigInt)(customerId).toString() : null,
                        guestUuid: guestUuid || null,
                    });
                }
                catch (toolCallError) {
                    console.warn('Failed to create tool call record:', toolCallError);
                }
            }
        }
        try {
            const messageSequence = toolCalls && toolCalls.length > 0 ? nextSequence + toolCalls.length : nextSequence;
            const created = await this.messageRepository.save({
                content: response,
                role: 'assistant',
                createdBy: ownerId,
                conversationId: conversationId.toString(),
                userId: userId ? (0, conversation_agent_1.toBigInt)(userId).toString() : null,
                customerId: customerId ? (0, conversation_agent_1.toBigInt)(customerId).toString() : null,
                guestUuid: guestUuid || null,
                sequence: messageSequence,
                cost: llmCosts?.totalCost,
                executionTime: llmCosts?.totalExecutionTime,
                inputTokens: llmCosts?.totalInputTokens,
                outputTokens: llmCosts?.totalOutputTokens,
            });
            await this.updateConversationTotals(conversationId);
            return created;
        }
        catch (error) {
            console.error('Failed to persist agent response:', error);
            try {
                const messageSequence = toolCalls && toolCalls.length > 0 ? nextSequence + toolCalls.length : nextSequence;
                const fallbackMessage = await this.messageRepository.save({
                    content: response || 'I apologize, but I encountered an error while processing your request.',
                    role: 'assistant',
                    createdBy: ownerId,
                    conversationId: conversationId.toString(),
                    userId: userId ? (0, conversation_agent_1.toBigInt)(userId).toString() : null,
                    customerId: customerId ? (0, conversation_agent_1.toBigInt)(customerId).toString() : null,
                    guestUuid: guestUuid || null,
                    sequence: messageSequence,
                    cost: llmCosts?.totalCost,
                    executionTime: llmCosts?.totalExecutionTime,
                    inputTokens: llmCosts?.totalInputTokens,
                    outputTokens: llmCosts?.totalOutputTokens,
                });
                await this.updateConversationTotals(conversationId);
                return fallbackMessage;
            }
            catch (fallbackError) {
                console.error('Fallback message creation also failed:', fallbackError);
                throw new Error(`Failed to save agent response: ${fallbackError instanceof Error ? fallbackError.message : String(fallbackError)}`);
            }
        }
    }
    async updateNotificationSafely(conversationUuid, notification, callbacks) {
        try {
            await this.conversationRepository.update({ uuid: conversationUuid }, { updatedAt: new Date() });
            await callbacks?.setNotification?.(conversationUuid, notification);
        }
        catch (error) {
            console.warn(`Failed to update notification status to ${notification}:`, error);
        }
    }
    async getConversationAndStoreInfo(conversationUuid) {
        const conversation = await this.getConversationByUuid(conversationUuid);
        if (!conversation) {
            throw new Error(`Conversation with UUID ${conversationUuid} not found`);
        }
        const storeCurrency = await this.getStoreCurrency(BigInt(conversation.storeId));
        const storePreferredLanguage = await this.getStorePreferredLanguage(BigInt(conversation.storeId));
        return {
            conversation,
            storeCurrency,
            storePreferredLanguage
        };
    }
    async getConversationHistory(conversationId) {
        const messageHistory = await this.messageRepository.find({
            where: { conversationId: conversationId.toString(), isDeleted: false },
            order: { createdAt: 'ASC' },
            select: ['content', 'role', 'userId'],
        });
        return messageHistory.map(msg => ({
            role: msg.role,
            content: msg.content
        }));
    }
    async getStoreProducts(storeId) {
        const products = await this.productRepository.find({
            where: {
                storeId: storeId.toString(),
                isDeleted: false
            },
            select: ['id', 'name', 'description', 'price', 'sku'],
            order: { createdAt: 'DESC' },
            take: 50
        });
        return products.map(product => ({
            id: BigInt(product.id),
            name: product.name,
            description: product.description,
            price: Number(product.price),
            sku: product.sku
        }));
    }
};
exports.ConversationService = ConversationService;
exports.ConversationService = ConversationService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(conversation_entity_1.Conversation)),
    __param(1, (0, typeorm_1.InjectRepository)(store_entity_1.Store)),
    __param(2, (0, typeorm_1.InjectRepository)(message_entity_1.Message)),
    __param(3, (0, typeorm_1.InjectRepository)(product_entity_1.Product)),
    __param(4, (0, typeorm_1.InjectRepository)(tool_call_entity_1.ToolCall)),
    __metadata("design:paramtypes", [typeorm_2.Repository,
        typeorm_2.Repository,
        typeorm_2.Repository,
        typeorm_2.Repository,
        typeorm_2.Repository,
        database_service_1.DatabaseService])
], ConversationService);
async function generateAgentMessage(db, params, callbacks) {
    const service = new ConversationService(db.getRepository(conversation_entity_1.Conversation), db.getRepository(store_entity_1.Store), db.getRepository(message_entity_1.Message), db.getRepository(product_entity_1.Product), db.getRepository(tool_call_entity_1.ToolCall), { getDataSource: () => db, isConnected: () => db.isInitialized });
    return service.generateAgentMessage(params, callbacks);
}
async function generateMessage(db, input, callbacks) {
    return generateAgentMessage(db, input, callbacks);
}
async function processUserMessage(db, params, callbacks) {
    return generateAgentMessage(db, params, callbacks);
}
//# sourceMappingURL=conversation.service.js.map