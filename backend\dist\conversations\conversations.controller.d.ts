import { ConversationsService } from './conversations.service';
import { Conversation } from './conversation.entity';
import { ToolCall } from './tool-call.entity';
export declare class ConversationsController {
    private readonly conversationsService;
    constructor(conversationsService: ConversationsService);
    create(createConversationDto: Partial<Conversation> & {
        userId?: string | number;
        storeId?: string | number;
        createdBy?: string | number;
    }): Promise<Conversation>;
    findAll(): Promise<Conversation[]>;
    findByStoreId(storeId: string): Promise<Conversation[]>;
    findByUserId(userId: string): Promise<Conversation[]>;
    findByUuid(uuid: string): Promise<Conversation>;
    getUnifiedTimelineByUuid(uuid: string, page?: string, limit?: string): Promise<any>;
    addMessageByUuid(uuid: string, messageData: {
        content: string;
        createdBy: string;
        agentId?: string;
        userId?: string;
        customerId?: string;
        guestUuid?: string;
        imageUrl?: string;
        videoUrl?: string;
        attachmentUrl?: string;
        attachmentType?: string;
        cost?: number;
        executionTime?: number;
        inputTokens?: number;
        outputTokens?: number;
    }): Promise<import("./message.entity").Message>;
    getTimeline(id: string, page?: string, limit?: string): Promise<any>;
    getUnifiedTimeline(id: string, page?: string, limit?: string): Promise<any>;
    addMessage(id: string, messageData: {
        content: string;
        senderType: string;
        senderId?: string | number;
    }): Promise<import("./message.entity").Message>;
    findOne(id: string): Promise<Conversation>;
    update(id: string, updateConversationDto: Partial<Conversation>): Promise<Conversation>;
    remove(id: string): Promise<void>;
    createToolCall(createToolCallDto: Partial<ToolCall> & {
        userId?: string | number;
        cost?: number;
        inputTokens?: number;
        outputTokens?: number;
        duration?: number;
    }): Promise<ToolCall>;
    addToolCallByUuid(uuid: string, toolCallData: {
        toolName: string;
        parameters: any;
        result?: any;
        status?: string;
        error?: string;
        duration?: number;
        cost?: number;
        inputTokens?: number;
        outputTokens?: number;
        createdBy: string;
        userId?: string;
    }): Promise<ToolCall>;
    findAllToolCalls(): Promise<ToolCall[]>;
    findToolCallsByConversationId(conversationId: string): Promise<ToolCall[]>;
    findToolCallById(id: string): Promise<ToolCall>;
    updateToolCall(id: string, updateToolCallDto: Partial<ToolCall>): Promise<ToolCall>;
    removeToolCall(id: string): Promise<void>;
}
