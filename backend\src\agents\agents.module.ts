import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { AgentsController } from './agents.controller';
import { AgentsService } from './agents.service';
import { Agent } from './agent.entity';
import { ConversationModule } from './core/conversation.module';

@Module({
  imports: [
    TypeOrmModule.forFeature([Agent]),
    ConversationModule,
  ],
  controllers: [AgentsController],
  providers: [AgentsService],
  exports: [AgentsService, ConversationModule],
})
export class AgentsModule {}
