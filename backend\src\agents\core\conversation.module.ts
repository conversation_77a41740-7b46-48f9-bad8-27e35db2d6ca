import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ConversationService } from './conversation.service';
import { Conversation } from '../../conversations/conversation.entity';
import { Store } from '../../stores/store.entity';
import { Message } from '../../conversations/message.entity';
import { Product } from '../../products/product.entity';
import { ToolCall } from '../../conversations/tool-call.entity';

@Module({
  imports: [TypeOrmModule.forFeature([Conversation, Store, Message, Product, ToolCall])],
  providers: [ConversationService],
  exports: [ConversationService],
})
export class ConversationModule {}
