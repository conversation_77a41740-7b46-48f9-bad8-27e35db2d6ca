import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, DataSource } from 'typeorm';
import {
	AgentRuntimeCallbacks,
	ConversationNotification,
	GenerateMessageInput,
	ProcessUserMessageInput,
	buildConversationAgent,
	buildProductContextMessage,
	buildCustomerContextMessage,
	ensureLLMProviderConfigured,
	toBigInt,
	getOwnerInfo,
} from './conversation-agent';

import { LlmApi, type Message as AgentMessage } from '../../../../llm-api/dist';
import { Conversation } from '../../conversations/conversation.entity';
import { Store } from '../../stores/store.entity';
import { Message } from '../../conversations/message.entity';
import { Product } from '../../products/product.entity';
import { ToolCall } from '../../conversations/tool-call.entity';
import { DatabaseService } from '../../shared/database.service';

@Injectable()
export class ConversationService {
	constructor(
		@InjectRepository(Conversation)
		private readonly conversationRepository: Repository<Conversation>,
		@InjectRepository(Store)
		private readonly storeRepository: Repository<Store>,
		@InjectRepository(Message)
		private readonly messageRepository: Repository<Message>,
		@InjectRepository(Product)
		private readonly productRepository: Repository<Product>,
		@InjectRepository(ToolCall)
		private readonly toolCallRepository: Repository<ToolCall>,
		private readonly databaseService: DatabaseService,
	) {}

	private get dataSource(): DataSource {
		return this.databaseService.getDataSource();
	}

	// ---- Service Methods ----

	async generateAgentMessage(
		params: ProcessUserMessageInput | GenerateMessageInput,
		callbacks?: AgentRuntimeCallbacks
	): Promise<string> {
		console.log(`🔧 generateAgentMessage called with:`, {
			dbType: typeof this.dataSource,
			dbExists: !!this.dataSource,
			dbInitialized: this.dataSource?.isInitialized,
			params,
			timestamp: new Date().toISOString()
		});

		await ensureLLMProviderConfigured();
		const { conversationUuid, userId, customerId, guestUuid, agentId } = params;
		// userMessage is optional - if not provided, we'll generate a continuation message
		const userMessage = 'userMessage' in params ? params.userMessage : undefined;

		const startTime = Date.now();

		try {
			// Validate database connection is still valid
			if (!this.dataSource || !this.dataSource.isInitialized) {
				console.error(`❌ Database connection validation failed in generateAgentMessage:`, {
					dbExists: !!this.dataSource,
					dbInitialized: this.dataSource?.isInitialized,
					dbType: typeof this.dataSource
				});
				throw new Error('Database connection is not properly initialized in generateAgentMessage');
			}

			console.log(`✅ Database connection validated in generateAgentMessage`);

			// Set initial thinking notification
			await this.updateNotificationSafely(conversationUuid, ConversationNotification.AgentIsThinking, callbacks);

			// Get conversation and store information
			const { conversation, storeCurrency, storePreferredLanguage } = await this.getConversationAndStoreInfo(conversationUuid);

			// Get products for the store
			const products = await this.getStoreProducts(BigInt(conversation.storeId));

			// Get customer information if conversation has a customer
			// Note: Customer functionality not yet implemented
			const customer = null;

			// Build conversation agent with basic context (no products/customer in system prompt)
			console.log(`🔧 Building conversation agent with database connection:`, {
				dbType: typeof this.dataSource,
				dbInitialized: this.dataSource?.isInitialized,
				conversationUuid,
				storeCurrency,
				storePreferredLanguage
			});
			const conversationAgent = buildConversationAgent(this.dataSource, conversationUuid, storeCurrency, storePreferredLanguage);

			// Get conversation history
			const history = await this.getConversationHistory(BigInt(conversation.id));

			// Build context messages
			const productContextMessage = buildProductContextMessage(products, storeCurrency);
			const customerContextMessage = buildCustomerContextMessage(customer);

			// Combine context messages with conversation history
			const allMessages: AgentMessage[] = [];

			// Add context messages (they now always return a message)
			if (productContextMessage) allMessages.push(productContextMessage);
			if (customerContextMessage) allMessages.push(customerContextMessage);

			// Add conversation history
			allMessages.push(...history);

			// Add the current user message or continuation prompt
			if (userMessage) {
				allMessages.push({ role: 'user', content: userMessage });
			} else {
				// For generateMessage functionality - continue the conversation
				allMessages.push({ role: 'user', content: 'Continue the conversation.' });
			}

			// Update notification to show response generation
			await this.updateNotificationSafely(conversationUuid, ConversationNotification.AgentIsGeneratingResponse, callbacks);

			// Generate response using the conversation agent
			const response = await LlmApi.generateLlmResponse(conversationAgent, allMessages);

			// const sanitizedResponse = sanitizeAssistantReply(response.content);
			const sanitizedResponse = response.content;

			const executionTime = Date.now() - startTime;

			// Extract metrics from the response
			const metrics = this.extractMetricsFromResponse(response);

			// Persist the final response with tool call information
			const created = await this.persistAgentResponse({
				agentId,
				userId,
				customerId,
				guestUuid,
				conversationId: BigInt(conversation.id),
				response: sanitizedResponse,
				toolCalls: response.calls,
				toolOutputs: response.outputs,
				executionDetails: response.executionDetails,
				llmCosts: {
					...metrics,
					totalExecutionTime: executionTime,
				},
			});

			// Clear notification status
			await this.updateNotificationSafely(conversationUuid, ConversationNotification.None, callbacks);

			return created.content;
		} catch (error) {
			// Ensure notification is cleared on any error
			await this.updateNotificationSafely(conversationUuid, ConversationNotification.None, callbacks);
			throw error;
		}
	}

	// Backward compatibility wrapper for generateMessage functionality
	async generateMessage(
		input: GenerateMessageInput,
		callbacks?: AgentRuntimeCallbacks
	): Promise<string> {
		// Call the unified generateAgentMessage function without a user message
		return this.generateAgentMessage(input, callbacks);
	}

	// Backward compatibility wrapper for processUserMessage functionality
	async processUserMessage(
		params: ProcessUserMessageInput,
		callbacks?: AgentRuntimeCallbacks
	): Promise<string> {
		// Call the unified generateAgentMessage function with a user message
		return this.generateAgentMessage(params, callbacks);
	}

	// ---- Private Helper Methods ----

	private async getConversationByUuid(conversationUuid: string) {
		return this.conversationRepository.findOne({
			where: { uuid: conversationUuid, isDeleted: false },
			select: ['id', 'storeId'],
		});
	}

	private async getStoreCurrency(storeId: bigint) {
		const store = await this.storeRepository.findOne({
			where: { id: storeId.toString(), isDeleted: false },
			select: ['currency'],
		});
		return store?.currency || 'USD';
	}

	private async getStorePreferredLanguage(storeId: bigint) {
		const store = await this.storeRepository.findOne({
			where: { id: storeId.toString(), isDeleted: false },
			select: ['preferredLanguage'],
		});
		return store?.preferredLanguage || 'en';
	}

	// Helper function to extract metrics from LlmResponseResult
	private extractMetricsFromResponse(response: any) {
		return {
			totalCost: response.totalCost || 0,
			totalInputTokens: response.totalInputTokens || 0,
			totalOutputTokens: response.totalOutputTokens || 0,
			totalExecutionTime: response.totalExecutionTime || 0,
		};
	}

	// Helper function to update conversation totals
	private async updateConversationTotals(conversationId: bigint) {
		try {
			// Update conversation timestamp
			// Note: Conversation entity doesn't have cost/token tracking fields
			// This would need to be added to the entity if tracking is required
			await this.conversationRepository.update(
				{ id: conversationId.toString() },
				{
					updatedAt: new Date(),
				}
			);
		} catch (error) {
			console.warn('Failed to update conversation totals:', error);
		}
	}

	// Helper function to persist agent response to database
	private async persistAgentResponse(params: {
		agentId: string;
		userId?: string | number | bigint;
		customerId?: string | number | bigint;
		guestUuid?: string;
		conversationId: bigint;
		response: string;
		toolCalls?: Array<{
			id: string;
			function: {
				name: string;
				arguments: string;
			};
		}>;
		toolOutputs?: Array<{
			role: string;
			content: string;
			tool_call_id: string;
		}>;
		executionDetails?: Array<{
			toolCallId: string;
			toolName: string;
			executionTime: number;
			success: boolean;
			errorMessage?: string;
			startTime: number;
			endTime: number;
			cost?: number;
			inputTokens?: number;
			outputTokens?: number;
		}>;
		llmCosts?: {
			totalCost: number;
			totalInputTokens: number;
			totalOutputTokens: number;
			totalExecutionTime?: number;
		};
	}): Promise<{ content: string }> {
		const { userId, customerId, guestUuid, conversationId, response, toolCalls, toolOutputs, executionDetails, llmCosts } = params;

		// Determine the owner info for database operations
		const { ownerId } = getOwnerInfo({ userId, customerId, guestUuid } as any);

		// Use timestamp-based ordering for sequence
		const nextSequence = Date.now();

		if (toolCalls && toolCalls.length > 0) {
			for (let i = 0; i < toolCalls.length; i++) {
				const toolCall = toolCalls[i];
				const toolOutput = toolOutputs?.find(to => to.tool_call_id === toolCall.id);
				const executionDetail = executionDetails?.find(ed => ed.toolCallId === toolCall.id);

				// Parse tool arguments
				let toolInput: any = {};
				try {
					toolInput = JSON.parse(toolCall.function.arguments);
				} catch (e) {
					toolInput = { raw: toolCall.function.arguments };
				}

				// Handle tool output - it might not be valid JSON
				let toolOutputData: any = undefined;
				if (toolOutput?.content) {
					try {
						// Try to parse as JSON first
						toolOutputData = JSON.parse(toolOutput.content);
					} catch (e) {
						// If it's not JSON, store as a string
						toolOutputData = { rawContent: toolOutput.content };
					}
				}

				// Create tool call record with the same sequence number
				try {
					await this.toolCallRepository.save({
						toolName: toolCall.function.name,
						parameters: toolInput || {}, // Ensure parameters is never null
						conversationId: conversationId.toString(),
						createdBy: ownerId,
						result: toolOutputData,
						duration: executionDetail?.executionTime || 0,
						status: executionDetail?.success ?? true ? 'completed' : 'failed',
						error: executionDetail?.errorMessage,
						userId: userId ? toBigInt(userId).toString() : null,
						customerId: customerId ? toBigInt(customerId).toString() : null,
						guestUuid: guestUuid || null,
					});
				} catch (toolCallError) {
					// Continue with other tool calls even if one fails
					console.warn('Failed to create tool call record:', toolCallError);
				}
			}
		}

		try {
			// Create the agent message with sequence number after all tool calls
			const messageSequence = toolCalls && toolCalls.length > 0 ? nextSequence + toolCalls.length : nextSequence;
			const created = await this.messageRepository.save({
				content: response,
				role: 'assistant', // Set role for agent messages
				createdBy: ownerId,
				conversationId: conversationId.toString(),
				userId: userId ? toBigInt(userId).toString() : null,
				customerId: customerId ? toBigInt(customerId).toString() : null,
				guestUuid: guestUuid || null,
				sequence: messageSequence,
				// Cost tracking fields
				cost: llmCosts?.totalCost,
				executionTime: llmCosts?.totalExecutionTime,
				inputTokens: llmCosts?.totalInputTokens,
				outputTokens: llmCosts?.totalOutputTokens,
			});

			// Update conversation totals after creating message and tool calls
			await this.updateConversationTotals(conversationId);

				return created;
		} catch (error) {
			console.error('Failed to persist agent response:', error);

			// Try to create with minimal data as fallback
			try {
				const messageSequence = toolCalls && toolCalls.length > 0 ? nextSequence + toolCalls.length : nextSequence;
				const fallbackMessage = await this.messageRepository.save({
					content: response || 'I apologize, but I encountered an error while processing your request.',
					role: 'assistant', // Set role for agent messages
					createdBy: ownerId,
					conversationId: conversationId.toString(),
					userId: userId ? toBigInt(userId).toString() : null,
					customerId: customerId ? toBigInt(customerId).toString() : null,
					guestUuid: guestUuid || null,
					sequence: messageSequence, // Use the sequence number after tool calls
					// Cost tracking fields
					cost: llmCosts?.totalCost,
					executionTime: llmCosts?.totalExecutionTime,
					inputTokens: llmCosts?.totalInputTokens,
					outputTokens: llmCosts?.totalOutputTokens,
				});

				// Update conversation totals even for fallback message
				await this.updateConversationTotals(conversationId);

				return fallbackMessage;
			} catch (fallbackError) {
				console.error('Fallback message creation also failed:', fallbackError);
				throw new Error(`Failed to save agent response: ${fallbackError instanceof Error ? fallbackError.message : String(fallbackError)}`);
			}
		}
	}

	// Helper function to safely update notification status
	private async updateNotificationSafely(
		conversationUuid: string,
		notification: ConversationNotification,
		callbacks?: AgentRuntimeCallbacks
	): Promise<void> {
		try {
			// Update the database (notificationStatus not available in current entity)
			await this.conversationRepository.update(
				{ uuid: conversationUuid },
				{ updatedAt: new Date() }
			);

			// Call the callback if provided
			await callbacks?.setNotification?.(conversationUuid, notification);
		} catch (error) {
			console.warn(`Failed to update notification status to ${notification}:`, error);
		}
	}

	// Helper function to get conversation and store information together
	private async getConversationAndStoreInfo(conversationUuid: string) {
		const conversation = await this.getConversationByUuid(conversationUuid);
		if (!conversation) {
			throw new Error(`Conversation with UUID ${conversationUuid} not found`);
		}

		const storeCurrency = await this.getStoreCurrency(BigInt(conversation.storeId));
		const storePreferredLanguage = await this.getStorePreferredLanguage(BigInt(conversation.storeId));

		return {
			conversation,
			storeCurrency,
			storePreferredLanguage
		};
	}

	// Helper function to get conversation history
	private async getConversationHistory(conversationId: bigint): Promise<AgentMessage[]> {
		const messageHistory = await this.messageRepository.find({
			where: { conversationId: conversationId.toString(), isDeleted: false },
			order: { createdAt: 'ASC' },
			select: ['content', 'role', 'userId'],
		});

		// Map messages to agent format (agentId and customerId not available in current entity)
		return messageHistory.map(msg => ({
			role: msg.role as 'user' | 'assistant',
			content: msg.content
		}));
	}

	// Helper function to get products for a store
	private async getStoreProducts(storeId: bigint): Promise<Array<{ id: bigint; name: string; description: string | null; price: number; sku: string | null }>> {
		const products = await this.productRepository.find({
			where: {
				storeId: storeId.toString(),
				isDeleted: false
			},
			select: ['id', 'name', 'description', 'price', 'sku'],
			order: { createdAt: 'DESC' },
			// Limit to prevent overwhelming the context
			take: 50
		});

		return products.map(product => ({
			id: BigInt(product.id),
			name: product.name,
			description: product.description,
			price: Number(product.price),
			sku: product.sku
		}));
	}
}





export async function generateAgentMessage(
	db: DataSource,
	params: ProcessUserMessageInput | GenerateMessageInput,
	callbacks?: AgentRuntimeCallbacks
): Promise<string> {
	// Create a temporary service instance for backward compatibility
	const service = new ConversationService(
		db.getRepository(Conversation),
		db.getRepository(Store),
		db.getRepository(Message),
		db.getRepository(Product),
		db.getRepository(ToolCall),
		{ getDataSource: () => db, isConnected: () => db.isInitialized } as DatabaseService
	);
	return service.generateAgentMessage(params, callbacks);
}

// Backward compatibility wrapper for generateMessage functionality
export async function generateMessage(
	db: DataSource,
	input: GenerateMessageInput,
	callbacks?: AgentRuntimeCallbacks
): Promise<string> {
	return generateAgentMessage(db, input, callbacks);
}

// Backward compatibility wrapper for processUserMessage functionality
export async function processUserMessage(
	db: DataSource,
	params: ProcessUserMessageInput,
	callbacks?: AgentRuntimeCallbacks
): Promise<string> {
	return generateAgentMessage(db, params, callbacks);
}


