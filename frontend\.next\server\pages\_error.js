/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "pages/_error";
exports.ids = ["pages/_error"];
exports.modules = {

/***/ "./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2F_error&preferredRegion=&absolutePagePath=private-next-pages%2F_error&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2F_error&preferredRegion=&absolutePagePath=private-next-pages%2F_error&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   config: () => (/* binding */ config),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   getServerSideProps: () => (/* binding */ getServerSideProps),\n/* harmony export */   getStaticPaths: () => (/* binding */ getStaticPaths),\n/* harmony export */   getStaticProps: () => (/* binding */ getStaticProps),\n/* harmony export */   reportWebVitals: () => (/* binding */ reportWebVitals),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   unstable_getServerProps: () => (/* binding */ unstable_getServerProps),\n/* harmony export */   unstable_getServerSideProps: () => (/* binding */ unstable_getServerSideProps),\n/* harmony export */   unstable_getStaticParams: () => (/* binding */ unstable_getStaticParams),\n/* harmony export */   unstable_getStaticPaths: () => (/* binding */ unstable_getStaticPaths),\n/* harmony export */   unstable_getStaticProps: () => (/* binding */ unstable_getStaticProps)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/pages/module.compiled */ \"./node_modules/next/dist/server/future/route-modules/pages/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/build/templates/helpers */ \"./node_modules/next/dist/build/templates/helpers.js\");\n/* harmony import */ var private_next_pages_document__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! private-next-pages/_document */ \"./node_modules/next/dist/pages/_document.js\");\n/* harmony import */ var private_next_pages_document__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(private_next_pages_document__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var private_next_pages_app__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! private-next-pages/_app */ \"./src/pages/_app.tsx\");\n/* harmony import */ var private_next_pages_error__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! private-next-pages/_error */ \"./node_modules/next/dist/pages/_error.js\");\n/* harmony import */ var private_next_pages_error__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(private_next_pages_error__WEBPACK_IMPORTED_MODULE_5__);\n\n\n\n// Import the app and document modules.\n\n\n// Import the userland code.\n\n// Re-export the component (should be the default export).\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(private_next_pages_error__WEBPACK_IMPORTED_MODULE_5__, \"default\"));\n// Re-export methods.\nconst getStaticProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(private_next_pages_error__WEBPACK_IMPORTED_MODULE_5__, \"getStaticProps\");\nconst getStaticPaths = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(private_next_pages_error__WEBPACK_IMPORTED_MODULE_5__, \"getStaticPaths\");\nconst getServerSideProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(private_next_pages_error__WEBPACK_IMPORTED_MODULE_5__, \"getServerSideProps\");\nconst config = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(private_next_pages_error__WEBPACK_IMPORTED_MODULE_5__, \"config\");\nconst reportWebVitals = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(private_next_pages_error__WEBPACK_IMPORTED_MODULE_5__, \"reportWebVitals\");\n// Re-export legacy methods.\nconst unstable_getStaticProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(private_next_pages_error__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getStaticProps\");\nconst unstable_getStaticPaths = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(private_next_pages_error__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getStaticPaths\");\nconst unstable_getStaticParams = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(private_next_pages_error__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getStaticParams\");\nconst unstable_getServerProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(private_next_pages_error__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getServerProps\");\nconst unstable_getServerSideProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(private_next_pages_error__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getServerSideProps\");\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0__.PagesRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.PAGES,\n        page: \"/_error\",\n        pathname: \"/_error\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\"\n    },\n    components: {\n        App: private_next_pages_app__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n        Document: (private_next_pages_document__WEBPACK_IMPORTED_MODULE_3___default())\n    },\n    userland: private_next_pages_error__WEBPACK_IMPORTED_MODULE_5__\n});\n\n//# sourceMappingURL=pages.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2F_error&preferredRegion=&absolutePagePath=private-next-pages%2F_error&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D!\n");

/***/ }),

/***/ "./src/context/AuthContext.tsx":
/*!*************************************!*\
  !*** ./src/context/AuthContext.tsx ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AuthProvider: () => (/* binding */ AuthProvider),\n/* harmony export */   useAuth: () => (/* binding */ useAuth)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _utils_auth__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../utils/auth */ \"./src/utils/auth.ts\");\n\n\n\nconst AuthContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nfunction AuthProvider({ children }) {\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const storageKey = \"teno:auth:user\";\n    const lastUserIdRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const refresh = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(async ()=>{\n        setIsLoading(true);\n        setError(null);\n        try {\n            const current = await (0,_utils_auth__WEBPACK_IMPORTED_MODULE_2__.getCurrentUser)();\n            setUser(current);\n            try {\n                if (current) {\n                    localStorage.setItem(storageKey, JSON.stringify(current));\n                } else {\n                    localStorage.removeItem(storageKey);\n                }\n            } catch  {}\n            try {\n                const prevId = lastUserIdRef.current;\n                const nextId = current?.id ?? null;\n                const changed = prevId !== nextId;\n                lastUserIdRef.current = nextId;\n                if (changed && \"undefined\" !== \"undefined\") {}\n            } catch  {}\n        } catch (err) {\n            console.warn(\"[AuthContext] Error during refresh:\", err);\n            setError(\"Failed to load user\");\n            setUser(null);\n            try {\n                localStorage.removeItem(storageKey);\n            } catch  {}\n        } finally{\n            setIsLoading(false);\n        }\n    }, []);\n    const loginWithGoogle = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((nextPath)=>{\n        (0,_utils_auth__WEBPACK_IMPORTED_MODULE_2__.redirectToGoogleAuth)(nextPath);\n    }, []);\n    const logout = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(async ()=>{\n        // Optimistically clear local state for snappier UX\n        setUser(null);\n        try {\n            localStorage.removeItem(storageKey);\n        } catch  {}\n        try {\n            await (0,_utils_auth__WEBPACK_IMPORTED_MODULE_2__.performLogout)();\n        } finally{\n            // Always refresh after logout to ensure backend/session is in sync\n            await refresh();\n        }\n    }, [\n        refresh\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // Optimistically hydrate from localStorage for fast first paint\n        try {\n            const raw = localStorage.getItem(storageKey);\n            if (raw) {\n                const cached = JSON.parse(raw);\n                setUser(cached);\n                lastUserIdRef.current = cached?.id ?? null;\n                // If we have a cached user, try to refresh from backend\n                refresh();\n            } else {\n                // No cached user, just set loading to false\n                setIsLoading(false);\n            }\n        } catch (e) {\n            console.warn(\"[AuthContext] Error reading cached user:\", e);\n            setIsLoading(false);\n        }\n        // Listen for global unauthorized signals to immediately drop user state\n        const onUnauthorized = ()=>{\n            (0,_utils_auth__WEBPACK_IMPORTED_MODULE_2__.clearClientAuthArtifacts)();\n            setUser(null);\n            lastUserIdRef.current = null;\n        };\n        if (false) {}\n        return ()=>{\n            if (false) {}\n        };\n    }, [\n        refresh\n    ]);\n    const value = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>({\n            user,\n            isLoading,\n            error,\n            refresh,\n            loginWithGoogle,\n            logout\n        }), [\n        user,\n        isLoading,\n        error,\n        refresh,\n        loginWithGoogle,\n        logout\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AuthContext.Provider, {\n        value: value,\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\context\\\\AuthContext.tsx\",\n        lineNumber: 117,\n        columnNumber: 5\n    }, this);\n}\nfunction useAuth() {\n    const ctx = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(AuthContext);\n    if (!ctx) {\n        throw new Error(\"useAuth must be used within an AuthProvider\");\n    }\n    return ctx;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/context/AuthContext.tsx\n");

/***/ }),

/***/ "./src/context/PreferencesContext.tsx":
/*!********************************************!*\
  !*** ./src/context/PreferencesContext.tsx ***!
  \********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   PreferencesProvider: () => (/* binding */ PreferencesProvider),\n/* harmony export */   usePreferences: () => (/* binding */ usePreferences)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _AuthContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./AuthContext */ \"./src/context/AuthContext.tsx\");\n/* harmony import */ var _utils_preferences__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../utils/preferences */ \"./src/utils/preferences.ts\");\n/* harmony import */ var _utils_auth__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../utils/auth */ \"./src/utils/auth.ts\");\n\n\n\n\n\nconst PreferencesContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nfunction getBrowserDefaults() {\n    const { language, country, currency } = (0,_utils_preferences__WEBPACK_IMPORTED_MODULE_3__.getBrowserPreferenceDefaults)();\n    return {\n        language,\n        country,\n        currency\n    };\n}\nfunction PreferencesProvider({ children }) {\n    const { user } = (0,_AuthContext__WEBPACK_IMPORTED_MODULE_2__.useAuth)();\n    const defaults = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>getBrowserDefaults(), []);\n    const [language, setLanguageState] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(defaults.language);\n    const [currency, setCurrencyState] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(defaults.currency);\n    const [country, setCountryState] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(defaults.country);\n    const storageKey = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>{\n        const userId = user?.id ? String(user.id) : \"anon\";\n        const key = `teno:prefs:${userId}`;\n        console.debug(\"[Prefs] storageKey computed\", {\n            userId,\n            key\n        });\n        return key;\n    }, [\n        user?.id\n    ]);\n    // Hydrate from localStorage on mount or when user changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        try {\n            const raw = localStorage.getItem(storageKey);\n            console.debug(\"[Prefs] hydrate start\", {\n                storageKey,\n                raw\n            });\n            if (raw) {\n                const parsed = JSON.parse(raw);\n                const nextLanguage = parsed.language || defaults.language;\n                const nextCurrency = parsed.currency || defaults.currency;\n                const nextCountry = parsed.country || defaults.country;\n                console.debug(\"[Prefs] hydrate parsed\", {\n                    parsed,\n                    nextLanguage,\n                    nextCurrency,\n                    nextCountry\n                });\n                setLanguageState(nextLanguage);\n                setCurrencyState(nextCurrency);\n                setCountryState(nextCountry);\n            } else {\n                console.debug(\"[Prefs] hydrate no existing, using defaults\", defaults);\n                setLanguageState(defaults.language);\n                setCurrencyState(defaults.currency);\n                setCountryState(defaults.country);\n            }\n        } catch  {\n            console.debug(\"[Prefs] hydrate error, falling back to defaults\", defaults);\n            setLanguageState(defaults.language);\n            setCurrencyState(defaults.currency);\n            setCountryState(defaults.country);\n        }\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n    }, [\n        storageKey\n    ]);\n    // Re-hydrate on auth changes (login/logout) since key scope changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const onAuthChange = ()=>{\n            try {\n                const raw = localStorage.getItem(storageKey);\n                if (raw) {\n                    const parsed = JSON.parse(raw);\n                    setLanguageState(parsed.language || defaults.language);\n                    setCurrencyState(parsed.currency || defaults.currency);\n                    setCountryState(parsed.country || defaults.country);\n                } else {\n                    setLanguageState(defaults.language);\n                    setCurrencyState(defaults.currency);\n                    setCountryState(defaults.country);\n                }\n            } catch  {\n                setLanguageState(defaults.language);\n                setCurrencyState(defaults.currency);\n                setCountryState(defaults.country);\n            }\n        };\n        if (false) {}\n        return ()=>{\n            if (false) {}\n        };\n    }, [\n        storageKey,\n        defaults.language,\n        defaults.currency,\n        defaults.country\n    ]);\n    // If a user is present, fetch server-side preferences and apply\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const userId = user?.id ? String(user.id) : null;\n        if (!userId) return;\n        let aborted = false;\n        (async ()=>{\n            try {\n                const url = `${(0,_utils_auth__WEBPACK_IMPORTED_MODULE_4__.getBackendUrl)()}/users/${encodeURIComponent(userId)}`;\n                console.debug(\"[Prefs] fetching server preferences\", {\n                    userId,\n                    url\n                });\n                const resp = await (0,_utils_auth__WEBPACK_IMPORTED_MODULE_4__.fetchWithCredentials)(url);\n                if (!resp.ok) return;\n                const payload = await resp.json();\n                if (aborted || !payload) return;\n                const nextLanguage = payload.preferredLanguage || defaults.language;\n                const nextCurrency = payload.preferredCurrency || defaults.currency;\n                const nextCountry = payload.countryCode || defaults.country;\n                console.debug(\"[Prefs] server preferences received\", {\n                    nextLanguage,\n                    nextCurrency,\n                    nextCountry\n                });\n                setLanguageState(nextLanguage);\n                setCurrencyState(nextCurrency);\n                setCountryState(nextCountry);\n                try {\n                    localStorage.setItem(storageKey, JSON.stringify({\n                        language: nextLanguage,\n                        currency: nextCurrency,\n                        country: nextCountry\n                    }));\n                } catch  {}\n            } catch  {}\n        })();\n        return ()=>{\n            aborted = true;\n        };\n    }, [\n        user?.id,\n        storageKey,\n        defaults.language,\n        defaults.currency,\n        defaults.country\n    ]);\n    const persist = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((next)=>{\n        try {\n            const current = {\n                language,\n                currency,\n                country\n            };\n            const merged = {\n                ...current,\n                ...next\n            };\n            console.debug(\"[Prefs] persist\", {\n                storageKey,\n                current,\n                next,\n                merged\n            });\n            localStorage.setItem(storageKey, JSON.stringify(merged));\n        } catch  {}\n    }, [\n        language,\n        currency,\n        country,\n        storageKey\n    ]);\n    const setLanguage = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((lng)=>{\n        setLanguageState(lng);\n        persist({\n            language: lng\n        });\n    }, [\n        persist\n    ]);\n    const setCurrency = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((cur)=>{\n        console.debug(\"[Prefs] setCurrency\", {\n            from: currency,\n            to: cur\n        });\n        setCurrencyState(cur);\n        persist({\n            currency: cur\n        });\n    }, [\n        persist,\n        currency\n    ]);\n    const setCountry = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((cc)=>{\n        setCountryState(cc);\n        persist({\n            country: cc\n        });\n    }, [\n        persist\n    ]);\n    const value = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>({\n            language,\n            currency,\n            country,\n            setLanguage,\n            setCurrency,\n            setCountry\n        }), [\n        language,\n        currency,\n        country,\n        setLanguage,\n        setCurrency,\n        setCountry\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(PreferencesContext.Provider, {\n        value: value,\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\context\\\\PreferencesContext.tsx\",\n        lineNumber: 169,\n        columnNumber: 3\n    }, this);\n}\nfunction usePreferences() {\n    const ctx = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(PreferencesContext);\n    if (!ctx) {\n        throw new Error(\"usePreferences must be used within a PreferencesProvider\");\n    }\n    return ctx;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/context/PreferencesContext.tsx\n");

/***/ }),

/***/ "./src/context/StoreContext.tsx":
/*!**************************************!*\
  !*** ./src/context/StoreContext.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   StoreProvider: () => (/* binding */ StoreProvider),\n/* harmony export */   useStore: () => (/* binding */ useStore)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _AuthContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./AuthContext */ \"./src/context/AuthContext.tsx\");\n\n\n\nconst StoreContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nfunction StoreProvider({ children }) {\n    const { user } = (0,_AuthContext__WEBPACK_IMPORTED_MODULE_2__.useAuth)();\n    const [currentStoreId, setCurrentStoreIdState] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const storageKey = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>{\n        const userId = user?.id ? String(user.id) : \"anon\";\n        return `teno:currentStoreId:${userId}`;\n    }, [\n        user?.id\n    ]);\n    // Hydrate from localStorage on mount or when user changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        try {\n            const raw = localStorage.getItem(storageKey);\n            if (raw) {\n                setCurrentStoreIdState(raw);\n            } else {\n                setCurrentStoreIdState(null);\n            }\n        } catch  {}\n    }, [\n        storageKey\n    ]);\n    // Clear store when user changes (login/logout) because key scope changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const onAuthChange = ()=>{\n            setCurrentStoreIdState(null);\n        };\n        if (false) {}\n        return ()=>{\n            if (false) {}\n        };\n    }, []);\n    const setCurrentStoreId = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((storeId)=>{\n        setCurrentStoreIdState(storeId);\n        try {\n            if (storeId) localStorage.setItem(storageKey, storeId);\n            else localStorage.removeItem(storageKey);\n        } catch  {}\n    }, [\n        storageKey\n    ]);\n    const clearStore = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        setCurrentStoreId(null);\n    }, [\n        setCurrentStoreId\n    ]);\n    const autoSelectFirstStore = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((stores)=>{\n        if (stores.length > 0 && !currentStoreId) {\n            const firstStore = stores[0];\n            setCurrentStoreId(firstStore.id);\n        }\n    }, [\n        currentStoreId,\n        setCurrentStoreId\n    ]);\n    const value = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>({\n            currentStoreId,\n            setCurrentStoreId,\n            clearStore,\n            autoSelectFirstStore\n        }), [\n        currentStoreId,\n        setCurrentStoreId,\n        clearStore,\n        autoSelectFirstStore\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(StoreContext.Provider, {\n        value: value,\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\context\\\\StoreContext.tsx\",\n        lineNumber: 76,\n        columnNumber: 5\n    }, this);\n}\nfunction useStore() {\n    const ctx = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(StoreContext);\n    if (!ctx) {\n        throw new Error(\"useStore must be used within a StoreProvider\");\n    }\n    return ctx;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/context/StoreContext.tsx\n");

/***/ }),

/***/ "./src/pages/_app.tsx":
/*!****************************!*\
  !*** ./src/pages/_app.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _styles_globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../styles/globals.css */ \"./src/styles/globals.css\");\n/* harmony import */ var _styles_globals_css__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(_styles_globals_css__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_EntityTable_fadein_css__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../components/EntityTable.fadein.css */ \"./src/components/EntityTable.fadein.css\");\n/* harmony import */ var _components_EntityTable_fadein_css__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(_components_EntityTable_fadein_css__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _context_AuthContext__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../context/AuthContext */ \"./src/context/AuthContext.tsx\");\n/* harmony import */ var _context_StoreContext__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../context/StoreContext */ \"./src/context/StoreContext.tsx\");\n/* harmony import */ var _context_PreferencesContext__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../context/PreferencesContext */ \"./src/context/PreferencesContext.tsx\");\n/* harmony import */ var _utils_useAuthGuard__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../utils/useAuthGuard */ \"./src/utils/useAuthGuard.ts\");\n\n\n\n\n\n\n\nfunction MyApp({ Component, pageProps }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_context_AuthContext__WEBPACK_IMPORTED_MODULE_3__.AuthProvider, {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_context_StoreContext__WEBPACK_IMPORTED_MODULE_4__.StoreProvider, {\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_context_PreferencesContext__WEBPACK_IMPORTED_MODULE_5__.PreferencesProvider, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(GuardedApp, {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Component, {\n                        ...pageProps\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\_app.tsx\",\n                        lineNumber: 15,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\_app.tsx\",\n                    lineNumber: 14,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\_app.tsx\",\n                lineNumber: 13,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\_app.tsx\",\n            lineNumber: 12,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\_app.tsx\",\n        lineNumber: 11,\n        columnNumber: 5\n    }, this);\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (MyApp);\nfunction GuardedApp({ children }) {\n    // Run global auth guard on every route navigation\n    (0,_utils_useAuthGuard__WEBPACK_IMPORTED_MODULE_6__.useAuthGuard)({\n        publicPaths: [\n            \"/\",\n            \"/login\",\n            \"/_error\",\n            \"/setup/store\",\n            \"/store/[storeUuid]\",\n            \"/storefront\",\n            \"/live/[uuid]\"\n        ]\n    });\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: children\n    }, void 0, false);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/pages/_app.tsx\n");

/***/ }),

/***/ "./src/utils/auth.ts":
/*!***************************!*\
  !*** ./src/utils/auth.ts ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   clearClientAuthArtifacts: () => (/* binding */ clearClientAuthArtifacts),\n/* harmony export */   debugLogin: () => (/* binding */ debugLogin),\n/* harmony export */   fetchWithCredentials: () => (/* binding */ fetchWithCredentials),\n/* harmony export */   getBackendUrl: () => (/* binding */ getBackendUrl),\n/* harmony export */   getCurrentUser: () => (/* binding */ getCurrentUser),\n/* harmony export */   handleUnauthorized: () => (/* binding */ handleUnauthorized),\n/* harmony export */   performLogout: () => (/* binding */ performLogout),\n/* harmony export */   redirectToGoogleAuth: () => (/* binding */ redirectToGoogleAuth)\n/* harmony export */ });\nconst getBackendUrl = ()=>{\n    return \"http://localhost:8000\" || 0;\n};\nconst clearClientAuthArtifacts = ()=>{\n    console.log(\"[clearClientAuthArtifacts] Clearing all auth artifacts\");\n    try {\n        // Clear local cache of user\n        if (typeof localStorage !== \"undefined\") {\n            localStorage.removeItem(\"teno:auth:user\");\n            localStorage.removeItem(\"teno:auth:token\");\n            console.log(\"[clearClientAuthArtifacts] Cleared localStorage tokens\");\n        }\n    } catch  {}\n    try {\n        // Proactively drop any readable client token if it exists\n        if (typeof document !== \"undefined\") {\n            // Expire both potential names just in case\n            document.cookie = \"access_token_client=; Path=/; Max-Age=0; SameSite=Lax\";\n            document.cookie = \"access_token=; Path=/; Max-Age=0; SameSite=Lax\";\n            console.log(\"[clearClientAuthArtifacts] Cleared cookie tokens\");\n        }\n    } catch  {}\n};\nconst handleUnauthorized = ()=>{\n    console.log(\"[handleUnauthorized] Called - clearing auth artifacts and redirecting to login\");\n    // Ensure client artifacts are cleared immediately\n    clearClientAuthArtifacts();\n    try {\n        // Notify any listeners (e.g., UI) that auth state became unauthorized\n        if (false) {}\n    } catch  {}\n    // Best-effort redirect to login preserving next path\n    try {\n        if (false) {}\n    } catch  {}\n};\nconst fetchWithCredentials = async (input, init)=>{\n    const headers = {\n        \"Content-Type\": \"application/json\",\n        ...init && init.headers ? init.headers : {}\n    };\n    // Try to get token from multiple sources in order of preference\n    try {\n        if (typeof document !== \"undefined\" && !headers[\"Authorization\"]) {\n            let token;\n            // First try cookie (primary method)\n            const cookieMatch = document.cookie.match(/(?:^|; )access_token_client=([^;]+)/);\n            if (cookieMatch) {\n                token = decodeURIComponent(cookieMatch[1]);\n                console.log(\"[fetchWithCredentials] Token found in cookie:\", token.substring(0, 20) + \"...\");\n            } else {\n                console.log(\"[fetchWithCredentials] No token found in cookie\");\n            }\n            // Fallback to localStorage if cookie not found\n            if (!token) {\n                try {\n                    token = localStorage.getItem(\"teno:auth:token\") || undefined;\n                    if (token) {\n                        console.log(\"[fetchWithCredentials] Token found in localStorage:\", token.substring(0, 20) + \"...\");\n                    } else {\n                        console.log(\"[fetchWithCredentials] No token found in localStorage\");\n                    }\n                } catch (e) {\n                    console.warn(\"Could not access localStorage:\", e);\n                }\n            }\n            if (token) {\n                headers[\"Authorization\"] = `Bearer ${token}`;\n                console.log(\"[fetchWithCredentials] Token added to Authorization header\");\n            } else {\n                console.log(\"[fetchWithCredentials] No token available for Authorization header\");\n            }\n        }\n    } catch (e) {\n        console.warn(\"Error getting auth token:\", e);\n    }\n    const response = await fetch(input, {\n        ...init,\n        credentials: \"include\",\n        headers\n    });\n    // If backend says unauthorized/forbidden, clear local auth and nudge UI\n    if (response.status === 401 || response.status === 403) {\n        handleUnauthorized();\n    }\n    return response;\n};\nconst getCurrentUser = async ()=>{\n    // Check if we have a token before making the API call\n    let hasToken = false;\n    try {\n        if (typeof document !== \"undefined\") {\n            const cookieMatch = document.cookie.match(/(?:^|; )access_token_client=([^;]+)/);\n            if (cookieMatch) {\n                hasToken = true;\n            }\n        }\n        if (!hasToken && typeof localStorage !== \"undefined\") {\n            try {\n                const token = localStorage.getItem(\"teno:auth:token\");\n                if (token) {\n                    hasToken = true;\n                }\n            } catch (e) {\n                console.warn(\"Could not access localStorage:\", e);\n            }\n        }\n    } catch (e) {\n        console.warn(\"Error checking for token:\", e);\n    }\n    if (!hasToken) {\n        console.log(\"[getCurrentUser] No token found, skipping API call\");\n        return null;\n    }\n    const url = `${getBackendUrl()}/auth/me`;\n    console.log(\"[getCurrentUser] Making API call to:\", url);\n    const response = await fetchWithCredentials(url);\n    if (!response.ok) {\n        console.log(\"[getCurrentUser] API call failed with status:\", response.status);\n        return null;\n    }\n    const data = await response.json();\n    console.log(\"[getCurrentUser] API call successful, user:\", data.user);\n    return data.user ?? null;\n};\nconst performLogout = async ()=>{\n    const url = `${getBackendUrl()}/auth/logout`;\n    try {\n        await fetchWithCredentials(url, {\n            method: \"POST\"\n        });\n    } finally{\n        // Always clear client artifacts regardless of server response\n        clearClientAuthArtifacts();\n    }\n};\nconst redirectToGoogleAuth = (nextPath)=>{\n    let url = `${getBackendUrl()}/auth/google`;\n    try {\n        // Prefer explicit nextPath, otherwise pick it up from current URL (?next=...)\n        let nextParam = nextPath;\n        if (!nextParam && \"undefined\" !== \"undefined\") {}\n        if (nextParam) {\n            // Only allow app-internal paths starting with '/'\n            const safeNext = decodeURIComponent(nextParam);\n            if (safeNext.startsWith(\"/\")) {\n                url += `?next=${encodeURIComponent(safeNext)}`;\n            }\n        }\n    } catch  {}\n    if (false) {}\n};\nconst debugLogin = async (email, name)=>{\n    const url = `${getBackendUrl()}/auth/dev-login`;\n    console.log(\"[debugLogin] Attempting debug login for:\", email);\n    const response = await fetch(url, {\n        method: \"POST\",\n        headers: {\n            \"Content-Type\": \"application/json\"\n        },\n        body: JSON.stringify({\n            email,\n            name\n        })\n    });\n    if (!response.ok) {\n        console.error(\"[debugLogin] Login failed with status:\", response.status);\n        return null;\n    }\n    const data = await response.json();\n    console.log(\"[debugLogin] Login successful, received data:\", data);\n    // Extract and store the token\n    if (data.access_token) {\n        console.log(\"[debugLogin] Storing access token\");\n        // Store in cookie\n        const isLocalhost =  false && (0);\n        const cookieOptions = isLocalhost ? `path=/; max-age=86400; samesite=lax` : `path=/; max-age=86400; secure; samesite=strict`;\n        document.cookie = `access_token_client=${encodeURIComponent(data.access_token)}; ${cookieOptions}`;\n        // Store in localStorage as backup\n        try {\n            localStorage.setItem(\"teno:auth:token\", data.access_token);\n        } catch (e) {\n            console.warn(\"[debugLogin] Could not store token in localStorage:\", e);\n        }\n        console.log(\"[debugLogin] Token stored successfully\");\n    } else {\n        console.warn(\"[debugLogin] No access_token in response\");\n    }\n    return data.user ?? null;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/utils/auth.ts\n");

/***/ }),

/***/ "./src/utils/preferences.ts":
/*!**********************************!*\
  !*** ./src/utils/preferences.ts ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CURRENCY_OPTIONS: () => (/* binding */ CURRENCY_OPTIONS),\n/* harmony export */   CURRENCY_SYMBOLS: () => (/* binding */ CURRENCY_SYMBOLS),\n/* harmony export */   DEFAULT_CURRENCY_BY_COUNTRY: () => (/* binding */ DEFAULT_CURRENCY_BY_COUNTRY),\n/* harmony export */   LANGUAGE_OPTIONS: () => (/* binding */ LANGUAGE_OPTIONS),\n/* harmony export */   TRANSLATIONS: () => (/* binding */ TRANSLATIONS),\n/* harmony export */   formatCurrency: () => (/* binding */ formatCurrency),\n/* harmony export */   getBrowserPreferenceDefaults: () => (/* binding */ getBrowserPreferenceDefaults),\n/* harmony export */   getStoreCurrency: () => (/* binding */ getStoreCurrency),\n/* harmony export */   getTranslation: () => (/* binding */ getTranslation)\n/* harmony export */ });\nconst LANGUAGE_OPTIONS = [\n    \"en\",\n    \"es\",\n    \"fr\",\n    \"de\",\n    \"it\",\n    \"pt\",\n    \"ru\",\n    \"zh\",\n    \"ja\",\n    \"ko\",\n    \"ar\",\n    \"hi\",\n    \"bn\",\n    \"pa\",\n    \"ur\",\n    \"fa\",\n    \"tr\",\n    \"nl\",\n    \"sv\",\n    \"no\",\n    \"da\",\n    \"fi\",\n    \"pl\",\n    \"cs\",\n    \"sk\",\n    \"hu\",\n    \"ro\",\n    \"bg\",\n    \"hr\",\n    \"sl\"\n];\nconst CURRENCY_OPTIONS = [\n    \"USD\",\n    \"EUR\",\n    \"DZD\",\n    \"GBP\",\n    \"CAD\",\n    \"AUD\",\n    \"NZD\",\n    \"JPY\",\n    \"CNY\",\n    \"HKD\",\n    \"SGD\",\n    \"INR\",\n    \"BRL\",\n    \"MXN\",\n    \"ZAR\",\n    \"SEK\",\n    \"NOK\",\n    \"DKK\",\n    \"CHF\",\n    \"PLN\",\n    \"CZK\",\n    \"HUF\",\n    \"ILS\",\n    \"TRY\",\n    \"AED\",\n    \"SAR\",\n    \"QAR\",\n    \"KWD\",\n    \"BHD\",\n    \"OMR\",\n    \"EGP\",\n    \"NGN\",\n    \"KES\",\n    \"ARS\",\n    \"CLP\",\n    \"COP\",\n    \"PEN\",\n    \"UYU\",\n    \"KRW\",\n    \"THB\",\n    \"MYR\",\n    \"PHP\",\n    \"IDR\"\n];\nconst CURRENCY_SYMBOLS = {\n    \"USD\": \"$\",\n    \"EUR\": \"€\",\n    \"DZD\": \"DZD\",\n    \"GBP\": \"\\xa3\",\n    \"CAD\": \"C$\",\n    \"AUD\": \"A$\",\n    \"NZD\": \"NZ$\",\n    \"JPY\": \"\\xa5\",\n    \"CNY\": \"\\xa5\",\n    \"HKD\": \"HK$\",\n    \"SGD\": \"S$\",\n    \"INR\": \"₹\",\n    \"BRL\": \"R$\",\n    \"MXN\": \"$\",\n    \"ZAR\": \"R\",\n    \"SEK\": \"kr\",\n    \"NOK\": \"kr\",\n    \"DKK\": \"kr\",\n    \"CHF\": \"CHF\",\n    \"PLN\": \"PLN\",\n    \"CZK\": \"CZK\",\n    \"HUF\": \"HUF\",\n    \"ILS\": \"₪\",\n    \"TRY\": \"₺\",\n    \"AED\": \"AED\",\n    \"SAR\": \"SAR\",\n    \"QAR\": \"QAR\",\n    \"KWD\": \"KWD\",\n    \"BHD\": \"BHD\",\n    \"OMR\": \"OMR\",\n    \"EGP\": \"EGP\",\n    \"NGN\": \"NGN\",\n    \"KES\": \"KES\",\n    \"ARS\": \"ARS\",\n    \"CLP\": \"CLP\",\n    \"COP\": \"COP\",\n    \"PEN\": \"PEN\",\n    \"UYU\": \"UYU\",\n    \"KRW\": \"₩\",\n    \"THB\": \"฿\",\n    \"MYR\": \"RM\",\n    \"PHP\": \"₱\",\n    \"IDR\": \"IDR\"\n};\nconst DEFAULT_CURRENCY_BY_COUNTRY = {\n    US: \"USD\",\n    GB: \"GBP\",\n    CA: \"CAD\",\n    AU: \"AUD\",\n    NZ: \"NZD\",\n    JP: \"JPY\",\n    CN: \"CNY\",\n    HK: \"HKD\",\n    SG: \"SGD\",\n    IN: \"INR\",\n    BR: \"BRL\",\n    MX: \"MXN\",\n    ZA: \"ZAR\",\n    SE: \"SEK\",\n    NO: \"NOK\",\n    DK: \"DKK\",\n    CH: \"CHF\",\n    PL: \"PLN\",\n    CZ: \"CZK\",\n    HU: \"HUF\",\n    IL: \"ILS\",\n    TR: \"TRY\",\n    AE: \"AED\",\n    SA: \"SAR\",\n    DZ: \"DZD\",\n    DE: \"EUR\",\n    FR: \"EUR\",\n    ES: \"EUR\",\n    IT: \"EUR\",\n    PT: \"EUR\"\n};\nfunction getBrowserPreferenceDefaults() {\n    let language = \"en\";\n    let country = \"US\";\n    try {\n        if (typeof navigator !== \"undefined\") {\n            const browserLang = navigator.language || \"en\";\n            // Convert browser language to supported format (e.g., 'en-US' -> 'en')\n            language = browserLang.split(\"-\")[0];\n            if (browserLang.split(\"-\").length > 1) country = browserLang.split(\"-\")[1].toUpperCase();\n        }\n    } catch  {}\n    const currency = DEFAULT_CURRENCY_BY_COUNTRY[country] || \"USD\";\n    return {\n        language,\n        country,\n        currency\n    };\n}\nfunction formatCurrency(amount, currency = \"USD\") {\n    const symbol = CURRENCY_SYMBOLS[currency] || currency;\n    return `${symbol}${amount.toLocaleString(\"en-US\", {\n        minimumFractionDigits: 2,\n        maximumFractionDigits: 2\n    })}`;\n}\n// Simple translation system for supported languages\nconst TRANSLATIONS = {\n    \"en\": {\n        \"storeDirectory\": \"Store Directory\",\n        \"browseStores\": \"Browse available stores and their products\",\n        \"visitStore\": \"Visit Store\",\n        \"shoppingCart\": \"Shopping Cart\",\n        \"addToCart\": \"Add to Cart\",\n        \"proceedToCheckout\": \"Proceed to Checkout\",\n        \"loading\": \"Loading...\",\n        \"noProducts\": \"No products available in this store.\",\n        \"howItWorks\": \"How it works\",\n        \"browseAndShop\": \"Browse stores, view their products, and add items to your cart. No account required - start shopping immediately!\"\n    },\n    \"fr\": {\n        \"storeDirectory\": \"R\\xe9pertoire des Magasins\",\n        \"browseStores\": \"Parcourez les magasins disponibles et leurs produits\",\n        \"visitStore\": \"Visiter le Magasin\",\n        \"shoppingCart\": \"Panier d'Achats\",\n        \"addToCart\": \"Ajouter au Panier\",\n        \"proceedToCheckout\": \"Proc\\xe9der au Paiement\",\n        \"loading\": \"Chargement...\",\n        \"noProducts\": \"Aucun produit disponible dans ce magasin.\",\n        \"howItWorks\": \"Comment \\xe7a marche\",\n        \"browseAndShop\": \"Parcourez les magasins, consultez leurs produits et ajoutez des articles \\xe0 votre panier. Aucun compte requis - commencez \\xe0 faire vos achats imm\\xe9diatement !\"\n    },\n    \"ar\": {\n        \"storeDirectory\": \"دليل المتاجر\",\n        \"browseStores\": \"تصفح المتاجر المتاحة ومنتجاتها\",\n        \"visitStore\": \"زيارة المتجر\",\n        \"shoppingCart\": \"سلة التسوق\",\n        \"addToCart\": \"إضافة إلى السلة\",\n        \"proceedToCheckout\": \"المتابعة للدفع\",\n        \"loading\": \"جاري التحميل...\",\n        \"noProducts\": \"لا توجد منتجات متاحة في هذا المتجر.\",\n        \"howItWorks\": \"كيف يعمل\",\n        \"browseAndShop\": \"تصفح المتاجر، عرض منتجاتها، وإضافة العناصر إلى سلة التسوق الخاصة بك. لا حاجة لحساب - ابدأ التسوق فوراً!\"\n    }\n};\nfunction getTranslation(key, language = \"en\") {\n    return TRANSLATIONS[language]?.[key] || TRANSLATIONS[\"en\"][key] || key;\n}\n// Get store currency - currently defaults to USD, but can be easily updated\n// to use store-specific currency when that field is added to the Store model\nfunction getStoreCurrency(store) {\n    // TODO: When store.currency field is added, use: return store?.currency || 'USD';\n    return \"USD\";\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9zcmMvdXRpbHMvcHJlZmVyZW5jZXMudHMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7O0FBSU8sTUFBTUEsbUJBQXdDO0lBQ3BEO0lBQU07SUFBTTtJQUFNO0lBQU07SUFBTTtJQUFNO0lBQU07SUFBTTtJQUFNO0lBQU07SUFBTTtJQUFNO0lBQU07SUFBTTtJQUFNO0lBQU07SUFBTTtJQUFNO0lBQU07SUFBTTtJQUFNO0lBQU07SUFBTTtJQUFNO0lBQU07SUFBTTtJQUFNO0lBQU07SUFBTTtDQUM5SyxDQUFDO0FBRUssTUFBTUMsbUJBQXdDO0lBQ3BEO0lBQU07SUFBTTtJQUFNO0lBQU07SUFBTTtJQUFNO0lBQU07SUFBTTtJQUFNO0lBQU07SUFBTTtJQUFNO0lBQU07SUFBTTtJQUFNO0lBQU07SUFBTTtJQUFNO0lBQU07SUFBTTtJQUFNO0lBQU07SUFBTTtJQUFNO0lBQU07SUFBTTtJQUFNO0lBQU07SUFBTTtJQUFNO0lBQU07SUFBTTtJQUFNO0lBQU07SUFBTTtJQUFNO0lBQU07SUFBTTtJQUFNO0lBQU07SUFBTTtJQUFNO0NBQzVQLENBQUM7QUFFSyxNQUFNQyxtQkFBc0Q7SUFDbEUsT0FBTztJQUNQLE9BQU87SUFDUCxPQUFPO0lBQ1AsT0FBTztJQUNQLE9BQU87SUFDUCxPQUFPO0lBQ1AsT0FBTztJQUNQLE9BQU87SUFDUCxPQUFPO0lBQ1AsT0FBTztJQUNQLE9BQU87SUFDUCxPQUFPO0lBQ1AsT0FBTztJQUNQLE9BQU87SUFDUCxPQUFPO0lBQ1AsT0FBTztJQUNQLE9BQU87SUFDUCxPQUFPO0lBQ1AsT0FBTztJQUNQLE9BQU87SUFDUCxPQUFPO0lBQ1AsT0FBTztJQUNQLE9BQU87SUFDUCxPQUFPO0lBQ1AsT0FBTztJQUNQLE9BQU87SUFDUCxPQUFPO0lBQ1AsT0FBTztJQUNQLE9BQU87SUFDUCxPQUFPO0lBQ1AsT0FBTztJQUNQLE9BQU87SUFDUCxPQUFPO0lBQ1AsT0FBTztJQUNQLE9BQU87SUFDUCxPQUFPO0lBQ1AsT0FBTztJQUNQLE9BQU87SUFDUCxPQUFPO0lBQ1AsT0FBTztJQUNQLE9BQU87SUFDUCxPQUFPO0lBQ1AsT0FBTztBQUNSLEVBQUU7QUFFSyxNQUFNQyw4QkFBaUU7SUFDN0VDLElBQUk7SUFDSkMsSUFBSTtJQUNKQyxJQUFJO0lBQ0pDLElBQUk7SUFDSkMsSUFBSTtJQUNKQyxJQUFJO0lBQ0pDLElBQUk7SUFDSkMsSUFBSTtJQUNKQyxJQUFJO0lBQ0pDLElBQUk7SUFDSkMsSUFBSTtJQUNKQyxJQUFJO0lBQ0pDLElBQUk7SUFDSkMsSUFBSTtJQUNKQyxJQUFJO0lBQ0pDLElBQUk7SUFDSkMsSUFBSTtJQUNKQyxJQUFJO0lBQ0pDLElBQUk7SUFDSkMsSUFBSTtJQUNKQyxJQUFJO0lBQ0pDLElBQUk7SUFDSkMsSUFBSTtJQUNKQyxJQUFJO0lBQ0pDLElBQUk7SUFDSkMsSUFBSTtJQUNKQyxJQUFJO0lBQ0pDLElBQUk7SUFDSkMsSUFBSTtJQUNKQyxJQUFJO0FBQ0wsRUFBRTtBQUVLLFNBQVNDO0lBQ2YsSUFBSUMsV0FBOEI7SUFDbEMsSUFBSUMsVUFBdUI7SUFDM0IsSUFBSTtRQUNILElBQUksT0FBT0MsY0FBYyxhQUFhO1lBQ3JDLE1BQU1DLGNBQWNELFVBQVVGLFFBQVEsSUFBSTtZQUMxQyx1RUFBdUU7WUFDdkVBLFdBQVdHLFlBQVlDLEtBQUssQ0FBQyxJQUFJLENBQUMsRUFBRTtZQUNwQyxJQUFJRCxZQUFZQyxLQUFLLENBQUMsS0FBS0MsTUFBTSxHQUFHLEdBQUdKLFVBQVVFLFlBQVlDLEtBQUssQ0FBQyxJQUFJLENBQUMsRUFBRSxDQUFDRSxXQUFXO1FBQ3ZGO0lBQ0QsRUFBRSxPQUFNLENBQUM7SUFDVCxNQUFNQyxXQUFXdkMsMkJBQTJCLENBQUNpQyxRQUFRLElBQUk7SUFDekQsT0FBTztRQUFFRDtRQUFVQztRQUFTTTtJQUFTO0FBQ3RDO0FBRU8sU0FBU0MsZUFBZUMsTUFBYyxFQUFFRixXQUE4QixLQUFLO0lBQ2pGLE1BQU1HLFNBQVMzQyxnQkFBZ0IsQ0FBQ3dDLFNBQVMsSUFBSUE7SUFFN0MsT0FBTyxDQUFDLEVBQUVHLE9BQU8sRUFBRUQsT0FBT0UsY0FBYyxDQUFDLFNBQVM7UUFDakRDLHVCQUF1QjtRQUN2QkMsdUJBQXVCO0lBQ3hCLEdBQUcsQ0FBQztBQUNMO0FBRUEsb0RBQW9EO0FBQzdDLE1BQU1DLGVBQWU7SUFDM0IsTUFBTTtRQUNMLGtCQUFrQjtRQUNsQixnQkFBZ0I7UUFDaEIsY0FBYztRQUNkLGdCQUFnQjtRQUNoQixhQUFhO1FBQ2IscUJBQXFCO1FBQ3JCLFdBQVc7UUFDWCxjQUFjO1FBQ2QsY0FBYztRQUNkLGlCQUFpQjtJQUNsQjtJQUNBLE1BQU07UUFDTCxrQkFBa0I7UUFDbEIsZ0JBQWdCO1FBQ2hCLGNBQWM7UUFDZCxnQkFBZ0I7UUFDaEIsYUFBYTtRQUNiLHFCQUFxQjtRQUNyQixXQUFXO1FBQ1gsY0FBYztRQUNkLGNBQWM7UUFDZCxpQkFBaUI7SUFDbEI7SUFDQSxNQUFNO1FBQ0wsa0JBQWtCO1FBQ2xCLGdCQUFnQjtRQUNoQixjQUFjO1FBQ2QsZ0JBQWdCO1FBQ2hCLGFBQWE7UUFDYixxQkFBcUI7UUFDckIsV0FBVztRQUNYLGNBQWM7UUFDZCxjQUFjO1FBQ2QsaUJBQWlCO0lBQ2xCO0FBQ0QsRUFBRTtBQUVLLFNBQVNDLGVBQWVDLEdBQW9DLEVBQUVoQixXQUE4QixJQUFJO0lBQzlGLE9BQU9jLFlBQVksQ0FBQ2QsU0FBc0MsRUFBRSxDQUFDZ0IsSUFBSSxJQUFJRixZQUFZLENBQUMsS0FBSyxDQUFDRSxJQUFJLElBQUlBO0FBQ3pHO0FBRUEsNEVBQTRFO0FBQzVFLDZFQUE2RTtBQUN0RSxTQUFTQyxpQkFBaUJDLEtBQVc7SUFDM0Msa0ZBQWtGO0lBQ2xGLE9BQU87QUFDUiIsInNvdXJjZXMiOlsid2VicGFjazovL3Rlbm8tc3RvcmUtZnJvbnRlbmQvLi9zcmMvdXRpbHMvcHJlZmVyZW5jZXMudHM/ZjMwZCJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgdHlwZSBTdXBwb3J0ZWRMYW5ndWFnZSA9IHN0cmluZztcbmV4cG9ydCB0eXBlIFN1cHBvcnRlZEN1cnJlbmN5ID0gc3RyaW5nO1xuZXhwb3J0IHR5cGUgQ291bnRyeUNvZGUgPSBzdHJpbmc7XG5cbmV4cG9ydCBjb25zdCBMQU5HVUFHRV9PUFRJT05TOiBTdXBwb3J0ZWRMYW5ndWFnZVtdID0gW1xuXHQnZW4nLCAnZXMnLCAnZnInLCAnZGUnLCAnaXQnLCAncHQnLCAncnUnLCAnemgnLCAnamEnLCAna28nLCAnYXInLCAnaGknLCAnYm4nLCAncGEnLCAndXInLCAnZmEnLCAndHInLCAnbmwnLCAnc3YnLCAnbm8nLCAnZGEnLCAnZmknLCAncGwnLCAnY3MnLCAnc2snLCAnaHUnLCAncm8nLCAnYmcnLCAnaHInLCAnc2wnXG5dO1xuXG5leHBvcnQgY29uc3QgQ1VSUkVOQ1lfT1BUSU9OUzogU3VwcG9ydGVkQ3VycmVuY3lbXSA9IFtcblx0J1VTRCcsJ0VVUicsJ0RaRCcsJ0dCUCcsJ0NBRCcsJ0FVRCcsJ05aRCcsJ0pQWScsJ0NOWScsJ0hLRCcsJ1NHRCcsJ0lOUicsJ0JSTCcsJ01YTicsJ1pBUicsJ1NFSycsJ05PSycsJ0RLSycsJ0NIRicsJ1BMTicsJ0NaSycsJ0hVRicsJ0lMUycsJ1RSWScsJ0FFRCcsJ1NBUicsJ1FBUicsJ0tXRCcsJ0JIRCcsJ09NUicsJ0VHUCcsJ05HTicsJ0tFUycsJ0FSUycsJ0NMUCcsJ0NPUCcsJ1BFTicsJ1VZVScsJ0tSVycsJ1RIQicsJ01ZUicsJ1BIUCcsJ0lEUidcbl07XG5cbmV4cG9ydCBjb25zdCBDVVJSRU5DWV9TWU1CT0xTOiBSZWNvcmQ8U3VwcG9ydGVkQ3VycmVuY3ksIHN0cmluZz4gPSB7XG5cdCdVU0QnOiAnJCcsXG5cdCdFVVInOiAn4oKsJyxcblx0J0RaRCc6ICdEWkQnLFxuXHQnR0JQJzogJ8KjJyxcblx0J0NBRCc6ICdDJCcsXG5cdCdBVUQnOiAnQSQnLFxuXHQnTlpEJzogJ05aJCcsXG5cdCdKUFknOiAnwqUnLFxuXHQnQ05ZJzogJ8KlJyxcblx0J0hLRCc6ICdISyQnLFxuXHQnU0dEJzogJ1MkJyxcblx0J0lOUic6ICfigrknLFxuXHQnQlJMJzogJ1IkJyxcblx0J01YTic6ICckJyxcblx0J1pBUic6ICdSJyxcblx0J1NFSyc6ICdrcicsXG5cdCdOT0snOiAna3InLFxuXHQnREtLJzogJ2tyJyxcblx0J0NIRic6ICdDSEYnLFxuXHQnUExOJzogJ1BMTicsXG5cdCdDWksnOiAnQ1pLJyxcblx0J0hVRic6ICdIVUYnLFxuXHQnSUxTJzogJ+KCqicsXG5cdCdUUlknOiAn4oK6Jyxcblx0J0FFRCc6ICdBRUQnLFxuXHQnU0FSJzogJ1NBUicsXG5cdCdRQVInOiAnUUFSJyxcblx0J0tXRCc6ICdLV0QnLFxuXHQnQkhEJzogJ0JIRCcsXG5cdCdPTVInOiAnT01SJyxcblx0J0VHUCc6ICdFR1AnLFxuXHQnTkdOJzogJ05HTicsXG5cdCdLRVMnOiAnS0VTJyxcblx0J0FSUyc6ICdBUlMnLFxuXHQnQ0xQJzogJ0NMUCcsXG5cdCdDT1AnOiAnQ09QJyxcblx0J1BFTic6ICdQRU4nLFxuXHQnVVlVJzogJ1VZVScsXG5cdCdLUlcnOiAn4oKpJyxcblx0J1RIQic6ICfguL8nLFxuXHQnTVlSJzogJ1JNJyxcblx0J1BIUCc6ICfigrEnLFxuXHQnSURSJzogJ0lEUicsXG59O1xuXG5leHBvcnQgY29uc3QgREVGQVVMVF9DVVJSRU5DWV9CWV9DT1VOVFJZOiBSZWNvcmQ8c3RyaW5nLCBTdXBwb3J0ZWRDdXJyZW5jeT4gPSB7XG5cdFVTOiAnVVNEJyxcblx0R0I6ICdHQlAnLFxuXHRDQTogJ0NBRCcsXG5cdEFVOiAnQVVEJyxcblx0Tlo6ICdOWkQnLFxuXHRKUDogJ0pQWScsXG5cdENOOiAnQ05ZJyxcblx0SEs6ICdIS0QnLFxuXHRTRzogJ1NHRCcsXG5cdElOOiAnSU5SJyxcblx0QlI6ICdCUkwnLFxuXHRNWDogJ01YTicsXG5cdFpBOiAnWkFSJyxcblx0U0U6ICdTRUsnLFxuXHROTzogJ05PSycsXG5cdERLOiAnREtLJyxcblx0Q0g6ICdDSEYnLFxuXHRQTDogJ1BMTicsXG5cdENaOiAnQ1pLJyxcblx0SFU6ICdIVUYnLFxuXHRJTDogJ0lMUycsXG5cdFRSOiAnVFJZJyxcblx0QUU6ICdBRUQnLFxuXHRTQTogJ1NBUicsXG5cdERaOiAnRFpEJyxcblx0REU6ICdFVVInLFxuXHRGUjogJ0VVUicsXG5cdEVTOiAnRVVSJyxcblx0SVQ6ICdFVVInLFxuXHRQVDogJ0VVUicsXG59O1xuXG5leHBvcnQgZnVuY3Rpb24gZ2V0QnJvd3NlclByZWZlcmVuY2VEZWZhdWx0cygpOiB7IGxhbmd1YWdlOiBTdXBwb3J0ZWRMYW5ndWFnZTsgY291bnRyeTogQ291bnRyeUNvZGU7IGN1cnJlbmN5OiBTdXBwb3J0ZWRDdXJyZW5jeSB9IHtcblx0bGV0IGxhbmd1YWdlOiBTdXBwb3J0ZWRMYW5ndWFnZSA9ICdlbic7XG5cdGxldCBjb3VudHJ5OiBDb3VudHJ5Q29kZSA9ICdVUyc7XG5cdHRyeSB7XG5cdFx0aWYgKHR5cGVvZiBuYXZpZ2F0b3IgIT09ICd1bmRlZmluZWQnKSB7XG5cdFx0XHRjb25zdCBicm93c2VyTGFuZyA9IG5hdmlnYXRvci5sYW5ndWFnZSB8fCAnZW4nO1xuXHRcdFx0Ly8gQ29udmVydCBicm93c2VyIGxhbmd1YWdlIHRvIHN1cHBvcnRlZCBmb3JtYXQgKGUuZy4sICdlbi1VUycgLT4gJ2VuJylcblx0XHRcdGxhbmd1YWdlID0gYnJvd3Nlckxhbmcuc3BsaXQoJy0nKVswXSBhcyBTdXBwb3J0ZWRMYW5ndWFnZTtcblx0XHRcdGlmIChicm93c2VyTGFuZy5zcGxpdCgnLScpLmxlbmd0aCA+IDEpIGNvdW50cnkgPSBicm93c2VyTGFuZy5zcGxpdCgnLScpWzFdLnRvVXBwZXJDYXNlKCk7XG5cdFx0fVxuXHR9IGNhdGNoIHt9XG5cdGNvbnN0IGN1cnJlbmN5ID0gREVGQVVMVF9DVVJSRU5DWV9CWV9DT1VOVFJZW2NvdW50cnldIHx8ICdVU0QnO1xuXHRyZXR1cm4geyBsYW5ndWFnZSwgY291bnRyeSwgY3VycmVuY3kgfTtcbn1cblxuZXhwb3J0IGZ1bmN0aW9uIGZvcm1hdEN1cnJlbmN5KGFtb3VudDogbnVtYmVyLCBjdXJyZW5jeTogU3VwcG9ydGVkQ3VycmVuY3kgPSAnVVNEJyk6IHN0cmluZyB7XG5cdGNvbnN0IHN5bWJvbCA9IENVUlJFTkNZX1NZTUJPTFNbY3VycmVuY3ldIHx8IGN1cnJlbmN5O1xuXG5cdHJldHVybiBgJHtzeW1ib2x9JHthbW91bnQudG9Mb2NhbGVTdHJpbmcoJ2VuLVVTJywge1xuXHRcdG1pbmltdW1GcmFjdGlvbkRpZ2l0czogMixcblx0XHRtYXhpbXVtRnJhY3Rpb25EaWdpdHM6IDIsXG5cdH0pfWA7XG59XG5cbi8vIFNpbXBsZSB0cmFuc2xhdGlvbiBzeXN0ZW0gZm9yIHN1cHBvcnRlZCBsYW5ndWFnZXNcbmV4cG9ydCBjb25zdCBUUkFOU0xBVElPTlMgPSB7XG5cdCdlbic6IHtcblx0XHQnc3RvcmVEaXJlY3RvcnknOiAnU3RvcmUgRGlyZWN0b3J5Jyxcblx0XHQnYnJvd3NlU3RvcmVzJzogJ0Jyb3dzZSBhdmFpbGFibGUgc3RvcmVzIGFuZCB0aGVpciBwcm9kdWN0cycsXG5cdFx0J3Zpc2l0U3RvcmUnOiAnVmlzaXQgU3RvcmUnLFxuXHRcdCdzaG9wcGluZ0NhcnQnOiAnU2hvcHBpbmcgQ2FydCcsXG5cdFx0J2FkZFRvQ2FydCc6ICdBZGQgdG8gQ2FydCcsXG5cdFx0J3Byb2NlZWRUb0NoZWNrb3V0JzogJ1Byb2NlZWQgdG8gQ2hlY2tvdXQnLFxuXHRcdCdsb2FkaW5nJzogJ0xvYWRpbmcuLi4nLFxuXHRcdCdub1Byb2R1Y3RzJzogJ05vIHByb2R1Y3RzIGF2YWlsYWJsZSBpbiB0aGlzIHN0b3JlLicsXG5cdFx0J2hvd0l0V29ya3MnOiAnSG93IGl0IHdvcmtzJyxcblx0XHQnYnJvd3NlQW5kU2hvcCc6ICdCcm93c2Ugc3RvcmVzLCB2aWV3IHRoZWlyIHByb2R1Y3RzLCBhbmQgYWRkIGl0ZW1zIHRvIHlvdXIgY2FydC4gTm8gYWNjb3VudCByZXF1aXJlZCAtIHN0YXJ0IHNob3BwaW5nIGltbWVkaWF0ZWx5ISdcblx0fSxcblx0J2ZyJzoge1xuXHRcdCdzdG9yZURpcmVjdG9yeSc6ICdSw6lwZXJ0b2lyZSBkZXMgTWFnYXNpbnMnLFxuXHRcdCdicm93c2VTdG9yZXMnOiAnUGFyY291cmV6IGxlcyBtYWdhc2lucyBkaXNwb25pYmxlcyBldCBsZXVycyBwcm9kdWl0cycsXG5cdFx0J3Zpc2l0U3RvcmUnOiAnVmlzaXRlciBsZSBNYWdhc2luJyxcblx0XHQnc2hvcHBpbmdDYXJ0JzogJ1BhbmllciBkXFwnQWNoYXRzJyxcblx0XHQnYWRkVG9DYXJ0JzogJ0Fqb3V0ZXIgYXUgUGFuaWVyJyxcblx0XHQncHJvY2VlZFRvQ2hlY2tvdXQnOiAnUHJvY8OpZGVyIGF1IFBhaWVtZW50Jyxcblx0XHQnbG9hZGluZyc6ICdDaGFyZ2VtZW50Li4uJyxcblx0XHQnbm9Qcm9kdWN0cyc6ICdBdWN1biBwcm9kdWl0IGRpc3BvbmlibGUgZGFucyBjZSBtYWdhc2luLicsXG5cdFx0J2hvd0l0V29ya3MnOiAnQ29tbWVudCDDp2EgbWFyY2hlJyxcblx0XHQnYnJvd3NlQW5kU2hvcCc6ICdQYXJjb3VyZXogbGVzIG1hZ2FzaW5zLCBjb25zdWx0ZXogbGV1cnMgcHJvZHVpdHMgZXQgYWpvdXRleiBkZXMgYXJ0aWNsZXMgw6Agdm90cmUgcGFuaWVyLiBBdWN1biBjb21wdGUgcmVxdWlzIC0gY29tbWVuY2V6IMOgIGZhaXJlIHZvcyBhY2hhdHMgaW1tw6lkaWF0ZW1lbnQgISdcblx0fSxcblx0J2FyJzoge1xuXHRcdCdzdG9yZURpcmVjdG9yeSc6ICfYr9mE2YrZhCDYp9mE2YXYqtin2KzYsScsXG5cdFx0J2Jyb3dzZVN0b3Jlcyc6ICfYqti12YHYrSDYp9mE2YXYqtin2KzYsSDYp9mE2YXYqtin2K3YqSDZiNmF2YbYqtis2KfYqtmH2KcnLFxuXHRcdCd2aXNpdFN0b3JlJzogJ9iy2YrYp9ix2Kkg2KfZhNmF2KrYrNixJyxcblx0XHQnc2hvcHBpbmdDYXJ0JzogJ9iz2YTYqSDYp9mE2KrYs9mI2YInLFxuXHRcdCdhZGRUb0NhcnQnOiAn2KXYttin2YHYqSDYpdmE2Ykg2KfZhNiz2YTYqScsXG5cdFx0J3Byb2NlZWRUb0NoZWNrb3V0JzogJ9in2YTZhdiq2KfYqNi52Kkg2YTZhNiv2YHYuScsXG5cdFx0J2xvYWRpbmcnOiAn2KzYp9ix2Yog2KfZhNiq2K3ZhdmK2YQuLi4nLFxuXHRcdCdub1Byb2R1Y3RzJzogJ9mE2Kcg2KrZiNis2K8g2YXZhtiq2KzYp9iqINmF2KrYp9it2Kkg2YHZiiDZh9iw2Kcg2KfZhNmF2KrYrNixLicsXG5cdFx0J2hvd0l0V29ya3MnOiAn2YPZitmBINmK2LnZhdmEJyxcblx0XHQnYnJvd3NlQW5kU2hvcCc6ICfYqti12YHYrSDYp9mE2YXYqtin2KzYsdiMINi52LHYtiDZhdmG2KrYrNin2KrZh9in2Iwg2YjYpdi22KfZgdipINin2YTYudmG2KfYtdixINil2YTZiSDYs9mE2Kkg2KfZhNiq2LPZiNmCINin2YTYrtin2LXYqSDYqNmDLiDZhNinINit2KfYrNipINmE2K3Ys9in2KggLSDYp9io2K/YoyDYp9mE2KrYs9mI2YIg2YHZiNix2KfZiyEnXG5cdH1cbn07XG5cbmV4cG9ydCBmdW5jdGlvbiBnZXRUcmFuc2xhdGlvbihrZXk6IGtleW9mIHR5cGVvZiBUUkFOU0xBVElPTlNbJ2VuJ10sIGxhbmd1YWdlOiBTdXBwb3J0ZWRMYW5ndWFnZSA9ICdlbicpOiBzdHJpbmcge1xuXHQgICAgICAgIHJldHVybiBUUkFOU0xBVElPTlNbbGFuZ3VhZ2UgYXMga2V5b2YgdHlwZW9mIFRSQU5TTEFUSU9OU10/LltrZXldIHx8IFRSQU5TTEFUSU9OU1snZW4nXVtrZXldIHx8IGtleTtcbn1cblxuLy8gR2V0IHN0b3JlIGN1cnJlbmN5IC0gY3VycmVudGx5IGRlZmF1bHRzIHRvIFVTRCwgYnV0IGNhbiBiZSBlYXNpbHkgdXBkYXRlZFxuLy8gdG8gdXNlIHN0b3JlLXNwZWNpZmljIGN1cnJlbmN5IHdoZW4gdGhhdCBmaWVsZCBpcyBhZGRlZCB0byB0aGUgU3RvcmUgbW9kZWxcbmV4cG9ydCBmdW5jdGlvbiBnZXRTdG9yZUN1cnJlbmN5KHN0b3JlPzogYW55KTogU3VwcG9ydGVkQ3VycmVuY3kge1xuXHQvLyBUT0RPOiBXaGVuIHN0b3JlLmN1cnJlbmN5IGZpZWxkIGlzIGFkZGVkLCB1c2U6IHJldHVybiBzdG9yZT8uY3VycmVuY3kgfHwgJ1VTRCc7XG5cdHJldHVybiAnVVNEJztcbn1cblxuXG4iXSwibmFtZXMiOlsiTEFOR1VBR0VfT1BUSU9OUyIsIkNVUlJFTkNZX09QVElPTlMiLCJDVVJSRU5DWV9TWU1CT0xTIiwiREVGQVVMVF9DVVJSRU5DWV9CWV9DT1VOVFJZIiwiVVMiLCJHQiIsIkNBIiwiQVUiLCJOWiIsIkpQIiwiQ04iLCJISyIsIlNHIiwiSU4iLCJCUiIsIk1YIiwiWkEiLCJTRSIsIk5PIiwiREsiLCJDSCIsIlBMIiwiQ1oiLCJIVSIsIklMIiwiVFIiLCJBRSIsIlNBIiwiRFoiLCJERSIsIkZSIiwiRVMiLCJJVCIsIlBUIiwiZ2V0QnJvd3NlclByZWZlcmVuY2VEZWZhdWx0cyIsImxhbmd1YWdlIiwiY291bnRyeSIsIm5hdmlnYXRvciIsImJyb3dzZXJMYW5nIiwic3BsaXQiLCJsZW5ndGgiLCJ0b1VwcGVyQ2FzZSIsImN1cnJlbmN5IiwiZm9ybWF0Q3VycmVuY3kiLCJhbW91bnQiLCJzeW1ib2wiLCJ0b0xvY2FsZVN0cmluZyIsIm1pbmltdW1GcmFjdGlvbkRpZ2l0cyIsIm1heGltdW1GcmFjdGlvbkRpZ2l0cyIsIlRSQU5TTEFUSU9OUyIsImdldFRyYW5zbGF0aW9uIiwia2V5IiwiZ2V0U3RvcmVDdXJyZW5jeSIsInN0b3JlIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///./src/utils/preferences.ts\n");

/***/ }),

/***/ "./src/utils/useAuthGuard.ts":
/*!***********************************!*\
  !*** ./src/utils/useAuthGuard.ts ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useAuthGuard: () => (/* binding */ useAuthGuard)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/router */ \"./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _context_AuthContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../context/AuthContext */ \"./src/context/AuthContext.tsx\");\n/* harmony import */ var _context_StoreContext__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../context/StoreContext */ \"./src/context/StoreContext.tsx\");\n\n\n\n\n// Default routes that don't require authentication\nconst DEFAULT_PUBLIC_ROUTES = [\n    \"/login\",\n    \"/auth/callback\",\n    \"/setup/store\"\n];\nfunction useAuthGuard(options = {}) {\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_1__.useRouter)();\n    const { user, isLoading, error } = (0,_context_AuthContext__WEBPACK_IMPORTED_MODULE_2__.useAuth)();\n    const { currentStoreId, setCurrentStoreId } = (0,_context_StoreContext__WEBPACK_IMPORTED_MODULE_3__.useStore)();\n    const [storesData, setStoresData] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    const [isStoresLoading, setIsStoresLoading] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);\n    const handledCache = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)({});\n    const isRedirectingRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(false);\n    // Combine default public routes with provided public paths\n    const publicRoutes = [\n        ...DEFAULT_PUBLIC_ROUTES,\n        ...options.publicPaths || []\n    ];\n    const isPublic = (path)=>{\n        return publicRoutes.some((route)=>{\n            // Handle dynamic routes like /live/[uuid]\n            if (route.includes(\"[\") && route.includes(\"]\")) {\n                const routePattern = route.replace(/\\[.*?\\]/g, \"[^/]+\");\n                const regex = new RegExp(`^${routePattern}$`);\n                return regex.test(path);\n            }\n            return path.startsWith(route);\n        });\n    };\n    // Fetch stores data for the current user\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(()=>{\n        if (!user) return;\n        const fetchStores = async ()=>{\n            setIsStoresLoading(true);\n            try {\n                // Import the store API dynamically to avoid circular dependencies\n                const { storeApi } = await __webpack_require__.e(/*! import() */ \"src_utils_api_ts\").then(__webpack_require__.bind(__webpack_require__, /*! ../utils/api */ \"./src/utils/api.ts\"));\n                const result = await storeApi.getByUserId(user.id, {\n                    page: 1,\n                    limit: 100\n                });\n                setStoresData(result);\n            } catch (error) {\n                console.error(\"Error fetching stores:\", error);\n                setStoresData({\n                    data: [],\n                    meta: {\n                        total: 0\n                    }\n                });\n            } finally{\n                setIsStoresLoading(false);\n            }\n        };\n        fetchStores();\n    }, [\n        user\n    ]);\n    // Main auth guard logic\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(()=>{\n        // Add a small delay to allow auth state to stabilize after token storage\n        const timeoutId = setTimeout(()=>{\n            // Avoid redirecting while we are still determining auth state\n            if (isLoading) return;\n            const currentPath = router.pathname;\n            const asPath = router.asPath;\n            const fullPath = asPath || currentPath;\n            const searchParams = new URLSearchParams( false ? 0 : \"\");\n            const loggedOutFlag = searchParams.get(\"loggedOut\");\n            // Ensure we only handle/log once per path after loading has completed\n            if (handledCache.current[currentPath]) {\n                return;\n            }\n            console.log(\"[AuthGuard] route/useEffect fired\", {\n                pathname: currentPath,\n                asPath: router.asPath,\n                isLoading,\n                hasUser: !!user,\n                error\n            });\n            if (!user && !isPublic(currentPath)) {\n                if (isRedirectingRef.current) {\n                    console.log(\"[AuthGuard] Redirect already in progress; skipping\");\n                    return;\n                }\n                isRedirectingRef.current = true;\n                const nextParam = encodeURIComponent(fullPath || \"/\");\n                const loginUrl = `/login?next=${nextParam}`;\n                console.log(\"[AuthGuard] Not authenticated; redirecting to\", loginUrl);\n                router.replace(loginUrl).finally(()=>{\n                    setTimeout(()=>{\n                        isRedirectingRef.current = false;\n                    }, 0);\n                });\n                handledCache.current[currentPath] = true;\n                return;\n            }\n            if (isPublic(currentPath) && currentPath === \"/login\" && loggedOutFlag === \"1\") {\n                handledCache.current[currentPath] = true;\n                return;\n            }\n            if (user && isPublic(currentPath) && currentPath === \"/login\") {\n                if (isStoresLoading) {\n                    return;\n                }\n                if (storesData === null) {\n                    return;\n                }\n                if (!isRedirectingRef.current) {\n                    isRedirectingRef.current = true;\n                    let target = \"/dashboard\";\n                    try {\n                        const nextParam = searchParams.get(\"next\") || undefined;\n                        const totalActive = storesData?.meta?.total ?? storesData?.data?.length ?? 0;\n                        if (nextParam && nextParam.startsWith(\"/\")) {\n                            target = nextParam;\n                        } else {\n                            if (totalActive === 0) {\n                                target = \"/setup/store\";\n                            }\n                        }\n                    } catch  {}\n                    console.log(\"[AuthGuard] Already authenticated; redirecting away from public auth page to\", target);\n                    router.replace(target).finally(()=>{\n                        setTimeout(()=>{\n                            isRedirectingRef.current = false;\n                        }, 0);\n                    });\n                }\n                handledCache.current[currentPath] = true;\n                return;\n            }\n            if (user && !isPublic(currentPath)) {\n                if (storesData === null) {\n                    console.log(\"[AuthGuard] Stores data not yet fetched, waiting...\");\n                    return;\n                }\n                const totalActive = storesData?.meta?.total ?? storesData?.data?.length ?? 0;\n                console.log(\"[AuthGuard] Store check:\", {\n                    currentPath,\n                    totalActive,\n                    storesData,\n                    isStoresLoading,\n                    loggedOutFlag\n                });\n                if (!isStoresLoading && totalActive === 0 && currentPath !== \"/setup/store\" && loggedOutFlag !== \"1\") {\n                    if (!isRedirectingRef.current) {\n                        isRedirectingRef.current = true;\n                        console.log(\"[AuthGuard] Authenticated user without active stores; redirecting to /setup/store\");\n                        router.replace(\"/setup/store\").finally(()=>{\n                            setTimeout(()=>{\n                                isRedirectingRef.current = false;\n                            }, 0);\n                        });\n                        handledCache.current[currentPath] = true;\n                        return;\n                    }\n                } else if (totalActive > 0 && !currentStoreId) {\n                    const firstId = storesData?.data?.[0]?.id;\n                    if (firstId) setCurrentStoreId(String(firstId));\n                }\n            }\n            if (!(user && !isPublic(currentPath) && isStoresLoading)) {\n                handledCache.current[currentPath] = true;\n            }\n        }, 100); // 100ms delay to allow auth state to stabilize\n        return ()=>clearTimeout(timeoutId);\n    }, [\n        router.pathname,\n        isLoading,\n        user,\n        error,\n        isStoresLoading,\n        storesData,\n        currentStoreId,\n        router,\n        setCurrentStoreId\n    ]);\n    return {\n        user,\n        isLoading,\n        error,\n        storesData,\n        isStoresLoading,\n        currentStoreId\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/utils/useAuthGuard.ts\n");

/***/ }),

/***/ "./src/components/EntityTable.fadein.css":
/*!***********************************************!*\
  !*** ./src/components/EntityTable.fadein.css ***!
  \***********************************************/
/***/ (() => {



/***/ }),

/***/ "./src/styles/globals.css":
/*!********************************!*\
  !*** ./src/styles/globals.css ***!
  \********************************/
/***/ (() => {



/***/ }),

/***/ "next/dist/compiled/next-server/pages.runtime.dev.js":
/*!**********************************************************************!*\
  !*** external "next/dist/compiled/next-server/pages.runtime.dev.js" ***!
  \**********************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/pages.runtime.dev.js");

/***/ }),

/***/ "react":
/*!************************!*\
  !*** external "react" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("react");

/***/ }),

/***/ "react-dom":
/*!****************************!*\
  !*** external "react-dom" ***!
  \****************************/
/***/ ((module) => {

"use strict";
module.exports = require("react-dom");

/***/ }),

/***/ "react/jsx-dev-runtime":
/*!****************************************!*\
  !*** external "react/jsx-dev-runtime" ***!
  \****************************************/
/***/ ((module) => {

"use strict";
module.exports = require("react/jsx-dev-runtime");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("fs");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@swc"], () => (__webpack_exec__("./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2F_error&preferredRegion=&absolutePagePath=private-next-pages%2F_error&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D!")));
module.exports = __webpack_exports__;

})();