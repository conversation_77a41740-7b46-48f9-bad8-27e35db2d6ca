import { useEffect, useMemo, useRef, useState } from 'react';
import { useRouter } from 'next/router';
import Head from 'next/head';
import { conversationApi, guestApi } from '../../utils/api';
import TimelineItem from '../../components/TimelineItem';

// Icon Components
const CustomerIcon = ({ name }: { name: string }) => (
  <div className="w-8 h-8 md:w-10 md:h-10 rounded-full bg-gradient-to-br from-orange-500 to-red-500 flex items-center justify-center text-white text-sm md:text-base font-bold shadow-lg border border-orange-400/30">
    {name.charAt(0).toUpperCase()}
  </div>
 );

export default function PublicLiveConversationPage() {
  const router = useRouter();
  const { uuid } = router.query;

  const [page] = useState(1);
  const [newMessage, setNewMessage] = useState('');
  const [conversation, setConversation] = useState<any>(null);
  const [timeline, setTimeline] = useState<any>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [guestName, setGuestName] = useState('');
  const [guestUuid, setGuestUuid] = useState<string | null>(null);
  const [isGuestSetup, setIsGuestSetup] = useState(false);
  const [isWaitingForAgent, setIsWaitingForAgent] = useState(false);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const waitingTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const limit = 50;

  const conversationUuid = useMemo(() => {
    // Wait for router to be ready and uuid to be available
    if (router.isReady && uuid) {
      return uuid as string;
    }
    return null;
  }, [router.isReady, uuid]);

  // Load guest data from localStorage on component mount
  useEffect(() => {
    if (typeof window !== 'undefined' && conversationUuid) {
      const guestStorageKey = `guestUuid_${conversationUuid}`;
      const nameStorageKey = `guestName_${conversationUuid}`;

      const savedGuestUuid = localStorage.getItem(guestStorageKey);
      const savedGuestName = localStorage.getItem(nameStorageKey);

      if (savedGuestUuid && savedGuestName) {
        setGuestUuid(savedGuestUuid);
        setGuestName(savedGuestName);
        setIsGuestSetup(true);
      }
    }
  }, [conversationUuid]);

  // Create or retrieve guest
  const setupGuest = async (name: string) => {
    if (!conversationUuid || !conversation?.storeId) return;

    try {
      setIsLoading(true);

      // Create guest using the backend API
      const guestData = {
        name: name.trim(),
        storeId: conversation.storeId.toString()
      };

      const guest = await guestApi.create(guestData);

      if (guest && guest.uuid) {
        // Save to localStorage
        const guestStorageKey = `guestUuid_${conversationUuid}`;
        const nameStorageKey = `guestName_${conversationUuid}`;

        localStorage.setItem(guestStorageKey, guest.uuid);
        localStorage.setItem(nameStorageKey, name.trim());

        setGuestUuid(guest.uuid);
        setGuestName(name.trim());
        setIsGuestSetup(true);
      }
    } catch (error) {
      console.error('Error creating guest:', error);
      setError('Failed to set up guest. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  // Fetch conversation data
  const fetchConversation = async () => {
    if (!conversationUuid || !router.isReady) return;
    
    try {
      const result = await conversationApi.getByUuid(conversationUuid);
      if (result && typeof result === 'object' && 'data' in result) {
        setConversation(result.data || result);
      } else {
        setConversation(result);
      }
    } catch (err) {
      console.error('Error fetching conversation:', err);
      setError('Failed to load conversation');
    }
  };

  // Fetch timeline data
  const fetchTimeline = async () => {
    if (!conversationUuid || !router.isReady) return;
    
    try {
      const result = await conversationApi.getUnifiedTimelineByUuid(conversationUuid, { page, limit });
      if (result && typeof result === 'object' && 'data' in result) {
        setTimeline(result.data || result);
      } else {
        setTimeline(result);
      }
    } catch (err) {
      console.error('Error fetching timeline:', err);
      setError('Failed to load timeline');
    }
  };

  // Fetch data when dependencies change
  useEffect(() => {
    if (conversationUuid && router.isReady) {
      fetchConversation();
      fetchTimeline();
    }
  }, [conversationUuid, router.isReady]);

  // Set up polling for real-time updates
  useEffect(() => {
    if (!conversationUuid || !router.isReady) return;

    const interval = setInterval(() => {
      fetchConversation();
      fetchTimeline();
    }, 2000); // Reduced from 5000ms to 2000ms for better responsiveness

    return () => {
      clearInterval(interval);
      // Clean up waiting timeout on unmount
      if (waitingTimeoutRef.current) {
        clearTimeout(waitingTimeoutRef.current);
      }
    };
  }, [conversationUuid, router.isReady]);

  const appendMessage = async (messageData: any) => {
    if (!conversationUuid || !guestUuid) return;

    try {
      console.log('🔧 Frontend: Sending message:', messageData);

      // Include guest UUID in the message data
      const messageWithGuest = {
        ...messageData,
        guestUuid: guestUuid,
        createdBy: guestName
      };

      const response = await conversationApi.appendMessageByUuid(conversationUuid, messageWithGuest);
      console.log('✅ Frontend: Message sent successfully:', response);
      setNewMessage('');

      // Set waiting state for agent response (only for user messages)
      const isUserMessage = !messageData.agentId || messageData.agentId === 'customer-message';
      if (isUserMessage) {
        setIsWaitingForAgent(true);

        // Clear any existing timeout
        if (waitingTimeoutRef.current) {
          clearTimeout(waitingTimeoutRef.current);
        }

        // Set a timeout to clear waiting state if no response comes (30 seconds)
        waitingTimeoutRef.current = setTimeout(() => {
          setIsWaitingForAgent(false);
        }, 30000);
      }

      // Refresh data
      fetchConversation();
      fetchTimeline();
      messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
    } catch (err) {
      console.error('❌ Frontend: Failed to send message:', err);
      setError('Failed to send message');
      setIsWaitingForAgent(false);
    }
  };

  // Use a ref to track previous message count to prevent unnecessary effects
  const prevMessageCountRef = useRef(0);
  const prevAgentMessageCountRef = useRef(0);

  useEffect(() => {
    const currentMessageCount = timeline?.timeline?.length || 0;
    if (currentMessageCount > prevMessageCountRef.current) {
      messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
      prevMessageCountRef.current = currentMessageCount;
    }

    // Check for new agent messages to clear waiting state
    if (timeline?.timeline) {
      const agentMessages = timeline.timeline.filter((item: any) =>
        item.type === 'message' && item.data?.metadata?.agentId &&
        item.data?.metadata?.agentId !== 'customer-message'
      );
      const currentAgentMessageCount = agentMessages.length;

      if (currentAgentMessageCount > prevAgentMessageCountRef.current && isWaitingForAgent) {
        setIsWaitingForAgent(false);
        // Clear the timeout since we got a response
        if (waitingTimeoutRef.current) {
          clearTimeout(waitingTimeoutRef.current);
          waitingTimeoutRef.current = null;
        }
        prevAgentMessageCountRef.current = currentAgentMessageCount;
      } else if (currentAgentMessageCount !== prevAgentMessageCountRef.current) {
        prevAgentMessageCountRef.current = currentAgentMessageCount;
      }
    }
  }, [timeline?.timeline?.length, timeline?.timeline, isWaitingForAgent]);

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage(e as any);
    }
  };

  const handleSendMessage = (e: React.FormEvent) => {
    e.preventDefault();
    if (!newMessage.trim() || !conversation) return;

    // Additional validation
    if (!conversation.uuid) {
      console.error('Conversation UUID is missing');
      return;
    }

    // Require guest setup
    if (!guestUuid || !guestName.trim()) {
      setError('Guest setup is required to send a message');
      return;
    }

    try {
      // Create guest message
      const messageData: any = {
        content: `[${guestName.trim()}]: ${newMessage.trim()}`,
        createdBy: guestName.trim(),
        agentId: 'customer-message', // Send as agent message to avoid user/customer constraints
        guestUuid: guestUuid,
      };

      appendMessage(messageData);
    } catch (error) {
      console.error('Failed to prepare message:', error);
    }
  };

  // Guest name setup modal
  if (!isGuestSetup && conversation) {
    return (
      <div className="min-h-screen bg-slate-950 relative overflow-hidden flex items-center justify-center">
        <Head>
          <title>Join Live Chat</title>
          <meta name="description" content="Join the live customer conversation" />
        </Head>
        
        {/* Animated Background */}
        <div className="absolute inset-0 opacity-30">
          <div className="absolute inset-0 bg-gradient-to-br from-blue-900/20 via-purple-900/20 to-cyan-900/20"></div>
          <div className="absolute inset-0" style={{
            backgroundImage: `
              radial-gradient(circle at 25% 25%, rgba(0, 255, 255, 0.1) 0%, transparent 50%),
              radial-gradient(circle at 75% 75%, rgba(255, 0, 255, 0.1) 0%, transparent 50%),
              radial-gradient(circle at 50% 50%, rgba(0, 255, 0, 0.05) 0%, transparent 50%)
            `
          }}></div>
        </div>

        <div className="relative z-10 max-w-md mx-auto px-6 py-8 bg-slate-900/50 border border-cyan-500/20 rounded-xl backdrop-blur-sm">
          <div className="text-center mb-6">
            <h1 className="text-2xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-cyan-400 to-blue-400 mb-2">
              Join Live Chat
            </h1>
            <p className="text-cyan-300/80">
              {conversation?.store?.name || 'Store'} - Live Conversation
            </p>
          </div>

          <form onSubmit={(e) => {
            e.preventDefault();
            if (guestName.trim()) {
              setupGuest(guestName.trim());
            }
          }} className="space-y-4">
            <div>
              <label htmlFor="guestName" className="block text-sm font-medium text-cyan-300 mb-2">
                Your Name
              </label>
              <input
                type="text"
                id="guestName"
                value={guestName}
                onChange={(e) => setGuestName(e.target.value)}
                placeholder="Enter your name"
                className="w-full px-4 py-3 bg-slate-800/50 border border-cyan-500/30 rounded-lg focus:outline-none focus:ring-2 focus:ring-cyan-400/50 focus:border-cyan-400 text-cyan-100 placeholder-cyan-400/50"
                required
                disabled={isLoading}
              />
            </div>

            {error && (
              <div className="text-red-400 text-sm text-center">
                {error}
              </div>
            )}

            <button
              type="submit"
              disabled={!guestName.trim() || isLoading}
              className="w-full px-6 py-3 rounded-lg bg-gradient-to-r from-cyan-500 to-blue-500 hover:from-cyan-400 hover:to-blue-400 text-white font-semibold disabled:opacity-40 disabled:cursor-not-allowed transition-all duration-200 transform hover:scale-105 active:scale-95"
            >
              {isLoading ? 'Setting up...' : 'Join Chat'}
            </button>
          </form>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-slate-950 relative overflow-hidden" style={{ fontFamily: 'system-ui, -apple-system, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif' }}>
      <Head>
        <title>{conversation?.title || 'Live Chat'}</title>
        <meta name="description" content="Live customer conversation" />
        <link rel="preconnect" href="https://fonts.googleapis.com" />
        <link rel="preconnect" href="https://fonts.gstatic.com" crossOrigin="anonymous" />
        <link href="https://fonts.googleapis.com/css2?family=Orbitron:wght@400;700;900&family=Exo+2:wght@300;400;500;600;700&display=swap" rel="stylesheet" />
      </Head>

      {/* Animated Background */}
      <div className="absolute inset-0 opacity-30">
        <div className="absolute inset-0 bg-gradient-to-br from-blue-900/20 via-purple-900/20 to-cyan-900/20"></div>
        <div className="absolute inset-0" style={{
          backgroundImage: `
            radial-gradient(circle at 25% 25%, rgba(0, 255, 255, 0.1) 0%, transparent 50%),
            radial-gradient(circle at 75% 75%, rgba(255, 0, 255, 0.1) 0%, transparent 50%),
            radial-gradient(circle at 50% 50%, rgba(0, 255, 0, 0.05) 0%, transparent 50%)
          `
        }}></div>
        <div className="absolute inset-0 opacity-10" style={{
          backgroundImage: `
            linear-gradient(90deg, transparent 98%, rgba(0, 255, 255, 0.3) 100%),
            linear-gradient(0deg, transparent 98%, rgba(0, 255, 255, 0.3) 100%)
          `,
          backgroundSize: '50px 50px'
        }}></div>
      </div>

      <div className="relative z-10 max-w-4xl mx-auto px-4 py-4 md:py-6 h-screen flex flex-col">
        {/* Header */}
        <div className="mb-4 md:mb-6">
          <h1 className="text-2xl md:text-3xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-cyan-400 to-blue-400" style={{ fontFamily: 'Orbitron, monospace' }}>
            {conversation?.store?.name || 'LIVE CHAT'}
          </h1>
          <div className="flex items-center justify-between mt-1">
            <p className="text-sm md:text-base text-cyan-300/80" style={{ fontFamily: 'Exo 2, sans-serif' }}>
              Live Chat - {guestName}
            </p>
            <button
              onClick={() => {
                // Clear guest data and show setup modal again
                if (typeof window !== 'undefined' && conversationUuid) {
                  const guestStorageKey = `guestUuid_${conversationUuid}`;
                  const nameStorageKey = `guestName_${conversationUuid}`;
                  localStorage.removeItem(guestStorageKey);
                  localStorage.removeItem(nameStorageKey);
                }
                setGuestUuid(null);
                setGuestName('');
                setIsGuestSetup(false);
              }}
              className="text-xs text-cyan-400/60 hover:text-cyan-400 transition-colors duration-200 underline"
              style={{ fontFamily: 'Exo 2, sans-serif' }}
            >
              Change Name
            </button>
          </div>
        </div>

        {/* Error State */}
        {error && (
          <div className="bg-red-500/10 border border-red-500/30 text-red-300 px-4 py-3 rounded-lg backdrop-blur-sm mb-4">
            <div className="flex items-center gap-2">
              <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
              </svg>
              {error}
            </div>
          </div>
        )}

        {/* Loading State */}
        {isLoading && (
          <div className="flex-1 flex items-center justify-center">
            <div className="text-center">
              <div className="relative w-16 h-16 mx-auto mb-4">
                <div className="absolute inset-0 border-4 border-cyan-400/20 rounded-full"></div>
                <div className="absolute inset-0 border-4 border-transparent border-t-cyan-400 rounded-full animate-spin"></div>
              </div>
              <p className="text-cyan-300/80" style={{ fontFamily: 'Exo 2, sans-serif' }}>
                Initializing Neural Interface...
              </p>
            </div>
          </div>
        )}

        {/* Chat Interface */}
        {conversation ? (
          <div className="flex-1 flex flex-col bg-slate-900/30 border border-cyan-500/20 rounded-xl backdrop-blur-sm overflow-hidden">
            {/* Messages Container */}
            <div className="flex-1 overflow-y-auto p-3 md:p-4 space-y-3 md:space-y-4 scrollbar-thin scrollbar-thumb-cyan-500/30 scrollbar-track-transparent">
              {(timeline?.timeline || []).map((item: any) => (
                <TimelineItem
                  key={`${item.type}-${item.id}`}
                  item={item}
                  showMetrics={false}
                  showToolCallDetails={false}
                />
              ))}
              {conversation?.notificationStatus && conversation.notificationStatus !== 'None' && (
                <div className="flex gap-3 md:gap-4 justify-start">
                  <div className="flex-shrink-0 mt-1">
                    <CustomerIcon name="AI" />
                  </div>
                  <div className="max-w-[85%] md:max-w-[70%]">
                    <div className="px-3 md:px-4 py-2 md:py-3 rounded-xl shadow-lg bg-gradient-to-br from-emerald-500/20 to-cyan-500/20 border border-emerald-400/30 text-emerald-100">
                      <div className="flex items-center gap-2">
                        <span className="text-xs opacity-80" style={{ fontFamily: 'Exo 2, sans-serif' }}>
                          {conversation.notificationStatus === 'AgentIsThinking'
                            ? 'AI Agent is thinking...'
                            : 'AI Agent is generating response...'}
                        </span>
                        <span className="inline-flex gap-1">
                          <span className="w-1.5 h-1.5 bg-emerald-300/80 rounded-full animate-bounce" />
                          <span className="w-1.5 h-1.5 bg-emerald-300/60 rounded-full animate-bounce [animation-delay:120ms]" />
                          <span className="w-1.5 h-1.5 bg-emerald-300/40 rounded-full animate-bounce [animation-delay:240ms]" />
                        </span>
                      </div>
                    </div>
                  </div>
                </div>
              )}
              {isWaitingForAgent && (!conversation?.notificationStatus || conversation.notificationStatus === 'None') && (
                <div className="flex gap-3 md:gap-4 justify-start">
                  <div className="flex-shrink-0 mt-1">
                    <CustomerIcon name="AI" />
                  </div>
                  <div className="max-w-[85%] md:max-w-[70%]">
                    <div className="px-3 md:px-4 py-2 md:py-3 rounded-xl shadow-lg bg-gradient-to-br from-emerald-500/20 to-cyan-500/20 border border-emerald-400/30 text-emerald-100">
                      <div className="flex items-center gap-2">
                        <span className="text-xs opacity-80" style={{ fontFamily: 'Exo 2, sans-serif' }}>
                          AI Agent is preparing response...
                        </span>
                        <span className="inline-flex gap-1">
                          <span className="w-1.5 h-1.5 bg-emerald-300/80 rounded-full animate-bounce" />
                          <span className="w-1.5 h-1.5 bg-emerald-300/60 rounded-full animate-bounce [animation-delay:120ms]" />
                          <span className="w-1.5 h-1.5 bg-emerald-300/40 rounded-full animate-bounce [animation-delay:240ms]" />
                        </span>
                      </div>
                    </div>
                  </div>
                </div>
              )}
              <div ref={messagesEndRef} />
            </div>

            {/* Message Input */}
            <form onSubmit={handleSendMessage} className="border-t border-cyan-500/20 p-3 md:p-4 bg-slate-900/50">
              <div className="flex gap-2 md:gap-3">
                <div className="flex-1 relative">
                  <textarea
                    value={newMessage}
                    onChange={(e) => setNewMessage(e.target.value)}
                    onKeyPress={handleKeyPress}
                    placeholder="Enter your message..."
                    className="w-full px-3 md:px-4 py-2 md:py-3 bg-slate-800/50 border border-cyan-500/30 rounded-lg focus:outline-none focus:ring-2 focus:ring-cyan-400/50 focus:border-cyan-400 resize-none text-cyan-100 placeholder-cyan-400/50 text-sm md:text-base transition-all duration-200"
                    style={{ fontFamily: 'Exo 2, sans-serif' }}
                    rows={1}
                    disabled={isLoading}
                  />
                  {isLoading && (
                    <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
                      <div className="w-4 h-4 border-2 border-cyan-400/30 border-t-cyan-400 rounded-full animate-spin"></div>
                    </div>
                  )}
                </div>
                <button
                  type="submit"
                  disabled={!newMessage.trim() || isLoading || !customerName.trim()}
                  className="px-4 md:px-6 py-2 md:py-3 rounded-lg bg-gradient-to-r from-cyan-500 to-blue-500 hover:from-cyan-400 hover:to-blue-400 text-white font-semibold disabled:opacity-40 disabled:cursor-not-allowed transition-all duration-200 transform hover:scale-105 active:scale-95 shadow-lg hover:shadow-cyan-500/25 text-sm md:text-base"
                  style={{ fontFamily: 'Exo 2, sans-serif' }}
                >
                  <svg className="w-4 h-4 md:w-5 md:h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8" />
                  </svg>
                </button>
              </div>
            </form>
          </div>
        ) : null}
      </div>

      <style jsx global>{`
        /* Custom scrollbar styles */
        .scrollbar-thin {
          scrollbar-width: thin;
        }
        .scrollbar-thumb-cyan-500\\/30::-webkit-scrollbar-thumb {
          background-color: rgba(6, 182, 212, 0.3);
          border-radius: 9999px;
        }
        .scrollbar-track-transparent::-webkit-scrollbar-track {
          background-color: transparent;
        }
        ::-webkit-scrollbar {
          width: 6px;
        }
        ::-webkit-scrollbar-track {
          background: transparent;
        }
        ::-webkit-scrollbar-thumb {
          background: rgba(6, 182, 212, 0.3);
          border-radius: 9999px;
        }
        ::-webkit-scrollbar-thumb:hover {
          background: rgba(6, 182, 212, 0.5);
        }
      `}</style>
    </div>
  );
}


